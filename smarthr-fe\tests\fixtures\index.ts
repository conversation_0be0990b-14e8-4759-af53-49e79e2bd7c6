/**
 * Test fixtures for Playwright E2E tests
 * Provides reusable test setup and teardown functionality
 */

import { test as base, Page, BrowserContext } from '@playwright/test';
import { AuthHelper, loginAsUser, TEST_USERS } from '../utils/auth';
import { DatabaseUtils } from '../utils/database';
import { generateTestPosition, generateTestCandidate, TestPosition, TestCandidate } from './test-data';
import { JobOrdersPage, JobDetailsPage, CandidatesPage } from '../pages';

// Extend the base test with custom fixtures
type TestFixtures = {
  // Authentication
  authHelper: AuthHelper;
  loggedInPage: Page;
  adminPage: Page;
  recruiterPage: Page;
  
  // Database utilities
  db: DatabaseUtils;
  
  // Page objects
  jobOrdersPage: JobOrdersPage;
  jobDetailsPage: JobDetailsPage;
  candidatesPage: CandidatesPage;
  
  // Test data
  testPosition: TestPosition;
  testCandidate: TestCandidate;
  createdPositionId: string;
  createdCandidateId: string;
};

export const test = base.extend<TestFixtures>({
  // Authentication helper
  authHelper: async ({ page, context }, use) => {
    const authHelper = new AuthHelper(page, context);
    await use(authHelper);
  },

  // Logged in page (default admin user)
  loggedInPage: async ({ page, context }, use) => {
    await loginAsUser(page, context, 'admin');
    await use(page);
  },

  // Admin user page
  adminPage: async ({ page, context }, use) => {
    await loginAsUser(page, context, 'admin');
    await use(page);
  },

  // Recruiter user page
  recruiterPage: async ({ page, context }, use) => {
    await loginAsUser(page, context, 'recruiter');
    await use(page);
  },

  // Database utilities
  db: async ({}, use) => {
    const db = DatabaseUtils.getInstance();
    await use(db);
    // Cleanup after test
    await db.cleanup();
  },

  // Page objects
  jobOrdersPage: async ({ loggedInPage }, use) => {
    const jobOrdersPage = new JobOrdersPage(loggedInPage);
    await use(jobOrdersPage);
  },

  jobDetailsPage: async ({ loggedInPage }, use) => {
    const jobDetailsPage = new JobDetailsPage(loggedInPage);
    await use(jobDetailsPage);
  },

  candidatesPage: async ({ loggedInPage }, use) => {
    const candidatesPage = new CandidatesPage(loggedInPage);
    await use(candidatesPage);
  },

  // Test data generators
  testPosition: async ({}, use) => {
    const position = generateTestPosition({
      positionName: `Test Position ${Date.now()}`,
      clientName: 'Test Client',
      seniority: 'mid'
    });
    await use(position);
  },

  testCandidate: async ({}, use) => {
    const candidate = generateTestCandidate({
      personalInfo: {
        firstName: 'Test',
        lastName: `Candidate${Date.now()}`,
        email: `test.candidate.${Date.now()}@example.com`,
        phone: '******-0123',
        location: 'Test City'
      }
    });
    await use(candidate);
  },

  // Created position ID (creates a position in the database)
  createdPositionId: async ({ db, testPosition }, use) => {
    const positionId = await db.createPosition(testPosition);
    await use(positionId);
    // Cleanup is handled by the db fixture
  },

  // Created candidate ID (creates a candidate in the database)
  createdCandidateId: async ({ db, testCandidate }, use) => {
    const candidateId = await db.createCandidate(testCandidate);
    await use(candidateId);
    // Cleanup is handled by the db fixture
  },
});

export { expect } from '@playwright/test';

/**
 * Test suite helpers
 */
export class TestSuite {
  /**
   * Setup test data for interview workflow tests
   */
  static async setupInterviewWorkflow(db: DatabaseUtils) {
    // Create test position
    const position = generateTestPosition({
      positionName: 'Senior Software Engineer',
      clientName: 'TechCorp Inc',
      seniority: 'senior'
    });
    const positionId = await db.createPosition(position);

    // Create test candidates
    const candidates = [
      generateTestCandidate({
        personalInfo: {
          firstName: 'Alice',
          lastName: 'Johnson',
          email: '<EMAIL>',
          phone: '******-0101',
          location: 'San Francisco'
        }
      }),
      generateTestCandidate({
        personalInfo: {
          firstName: 'Bob',
          lastName: 'Smith',
          email: '<EMAIL>',
          phone: '******-0102',
          location: 'New York'
        }
      })
    ];

    const candidateIds = [];
    for (const candidate of candidates) {
      const candidateId = await db.createCandidate(candidate);
      candidateIds.push(candidateId);
    }

    // Generate interview questions
    await db.generateQuestions(positionId, 5);

    return {
      positionId,
      candidateIds,
      position,
      candidates
    };
  }

  /**
   * Clean up test data
   */
  static async cleanup(db: DatabaseUtils) {
    await db.cleanup();
  }
}

/**
 * Common test patterns
 */
export const testPatterns = {
  /**
   * Test that requires a position with candidates
   */
  withPositionAndCandidates: test.extend<{
    setupData: { positionId: string; candidateIds: string[] };
  }>({
    setupData: async ({ db }, use) => {
      const data = await TestSuite.setupInterviewWorkflow(db);
      await use(data);
    },
  }),

  /**
   * Test that requires generated interview questions
   */
  withInterviewQuestions: test.extend<{
    positionWithQuestions: string;
  }>({
    positionWithQuestions: async ({ db, createdPositionId }, use) => {
      await db.generateQuestions(createdPositionId, 10);
      await use(createdPositionId);
    },
  }),
};

/**
 * Test data constants
 */
export const TEST_CONSTANTS = {
  TIMEOUTS: {
    SHORT: 5000,
    MEDIUM: 10000,
    LONG: 30000,
    VERY_LONG: 60000,
  },
  
  SELECTORS: {
    LOADING: '.ant-spin-spinning',
    NOTIFICATION: '.ant-message, .ant-notification',
    MODAL: '.ant-modal',
    DRAWER: '.ant-drawer',
    TABLE: '.ant-table-tbody',
    EMPTY_STATE: '.ant-empty',
  },
  
  MESSAGES: {
    SUCCESS: {
      POSITION_CREATED: 'Position created successfully',
      CANDIDATE_CREATED: 'Candidate created successfully',
      QUESTIONS_GENERATED: 'Questions generated successfully',
      INTERVIEW_CREATED: 'Interview created successfully',
    },
    ERROR: {
      NETWORK_ERROR: 'Network error',
      VALIDATION_ERROR: 'Validation error',
      NOT_FOUND: 'Not found',
    },
  },
};

/**
 * Utility functions for tests
 */
export const testUtils = {
  /**
   * Wait for a specific amount of time
   */
  wait: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),

  /**
   * Generate unique test identifier
   */
  generateTestId: () => `test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,

  /**
   * Format date for testing
   */
  formatDate: (date: Date) => date.toISOString().split('T')[0],

  /**
   * Create test email
   */
  createTestEmail: (prefix: string) => `${prefix}.${Date.now()}@test.smarthr.com`,
};
