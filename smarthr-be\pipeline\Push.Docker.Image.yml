resources:
  repositories:
    - repository: self
      type: git
      name: test_SmartHR
      ref: refs/heads/main
      trigger:
        branches:
          include:
            - refs/heads/main

pool:
  vmImage: 'ubuntu-latest'

variables:
  - name: dockerAzureSubscription
    value: 'shr_acr_subscription'

steps:
  - checkout: self
  
  - powershell: Get-ChildItem -Path $(Build.SourcesDirectory) -Recurse -Force
    displayName: 'Listing Checkout files'
  
  - task: Docker@2
    displayName: 'Login to ACR'
    inputs:
      command: 'login'
      containerRegistry: $(dockerAzureSubscription)
  
  - task: Docker@2
    displayName: 'Build Docker Image'
    inputs:
      command: 'build'
      containerRegistry: $(dockerAzureSubscription)
      repository: 'test.smart.hr'
      dockerfile: $(Build.SourcesDirectory)/Dockerfile
      tags: 'latest'
    continueOnError: false
  
  - script: |
      docker images
    displayName: 'Show Images'
  
  - task: Docker@2
    displayName: 'Push Docker Image'
    inputs:
      command: 'push'
      containerRegistry: $(dockerAzureSubscription)
      repository: 'test.smart.hr'
      tags: 'latest'
