import React, { useState, useEffect } from 'react';
import { Table, Tag, Input, Row, Typography, Card } from 'antd';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useFetch } from '../../hooks/useFetch';
import { useDebounce } from '../../hooks/useDebounce';
import type { PositionsResponse, Job } from '../../types/job';
import type { JobFilter, FilterOption } from '../../types/filters';
import { endpoints } from '../../utils/api';
import { useNavigation } from '../../contexts/NavigationContext';
import { TableFilterController } from '../common/TableFilterController';
import { FilterOptionsService } from '../../services/filterOptionsService';
import { formatDateTime } from '../../utils/dateUtils';

const { Search } = Input;
const { Title } = Typography;

export const JobOrders: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { setPreviousPath, setJobOrdersState } = useNavigation();

  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useDebounce('', 300);

  // New filter state using JobFilter type
  const [filters, setFilters] = useState<JobFilter>({
    stage: 'all',
    clientName: '',
    location: '',
    createdFrom: '',
    createdTo: '',
    searchTerm: ''
  });

  // Filter options state
  const [filterOptions, setFilterOptions] = useState<{
    clients?: FilterOption[];
    locations?: FilterOption[];
  }>({});
  const [filterOptionsLoading, setFilterOptionsLoading] = useState(false);

  useEffect(() => {
    setDebouncedSearchTerm(searchTerm);
    setFilters((prev) => ({ ...prev, searchTerm: searchTerm || '' }));
    setPage(1);
  }, [searchTerm]);

  // Load filter options
  useEffect(() => {
    const loadFilterOptions = async () => {
      setFilterOptionsLoading(true);
      try {
        const [clients, locations] = await Promise.all([
          FilterOptionsService.getClients(),
          FilterOptionsService.getLocations()
        ]);
        setFilterOptions({ clients, locations });
      } catch (error) {
        console.error('Error loading filter options:', error);
      } finally {
        setFilterOptionsLoading(false);
      }
    };

    loadFilterOptions();
  }, []);

  // Restore state when component mounts if there's preserved state
  useEffect(() => {
    const preservedState = location.state?.preservedState;
    if (preservedState) {
      setSearchTerm(preservedState.searchTerm);
      setPage(preservedState.page);
      setLimit(preservedState.limit);
      setFilters({
        stage: preservedState.stage || 'all',
        clientName: preservedState.clientName || '',
        location: preservedState.location || '',
        createdFrom: preservedState.createdFrom || '',
        createdTo: preservedState.createdTo || '',
        searchTerm: preservedState.searchTerm || ''
      });
      // Clear the state from location to prevent repeated restoration
      window.history.replaceState({}, '');
    }
  }, [location.state]);

  // Helper function to convert stage filter to API format
  const getStageForAPI = (stage: string) => {
    if (stage === 'open') return true;
    if (stage === 'closed') return false;
    return undefined; // Don't send stage param when 'all' is selected
  };

  // Function to save current state before navigation
  const saveCurrentState = () => {
    setPreviousPath('/');
    setJobOrdersState({
      searchTerm,
      page,
      limit,
      stage: filters.stage || 'all',
      clientName: filters.clientName || '',
      location: filters.location || '',
      createdFrom: filters.createdFrom || '',
      createdTo: filters.createdTo || ''
    });
  };

  const { data, loading, error } = useFetch<PositionsResponse>({
    url: endpoints.positions.list,
    method: 'POST',
    params: {
      chunk_size: limit,
      page
    },
    body: {
      ...(getStageForAPI(filters.stage || 'all') !== undefined && {
        stage: getStageForAPI(filters.stage || 'all')
      }),
      search_term: debouncedSearchTerm ? searchTerm.toLowerCase() : '',
      client_name: filters.clientName,
      location: filters.location,
      created_from: filters.createdFrom,
      created_to: filters.createdTo
    },
    dependencies: [
      limit,
      page,
      debouncedSearchTerm,
      filters.stage,
      filters.clientName,
      filters.location,
      filters.createdFrom,
      filters.createdTo
    ]
  });

  // Filter handlers
  const handleFiltersChange = (newFilters: JobFilter) => {
    setFilters(newFilters);
    // Don't reset page here - wait for apply
  };

  const handleClearFilters = () => {
    setFilters({
      stage: 'all',
      clientName: '',
      location: '',
      createdFrom: '',
      createdTo: '',
      searchTerm: ''
    });
    setPage(1);
  };

  const handleApplyFilters = () => {
    setPage(1); // Reset to first page when filters are applied
    // The API will be called automatically due to dependency changes
  };

  if (error) return <p>Error: {error}</p>;

  const handleRowClick = (record: Job) => {
    saveCurrentState();
    navigate(`/job/${record.id}`, { state: { job: record } });
  };

  // Function to apply default sorting: Open positions first, then by created date newest to oldest
  const getSortedData = (data: Job[] | undefined) => {
    if (!data) return [];

    return [...data].sort((a, b) => {
      // First, sort by stage (Open positions first)
      const aIsOpen = !a.position_info?.reasonStatus?.reason;
      const bIsOpen = !b.position_info?.reasonStatus?.reason;

      if (aIsOpen !== bIsOpen) {
        return aIsOpen ? -1 : 1; // Open positions (true) come first
      }

      // Within same stage, sort by created date (newest first)
      const aDate = new Date(a.created_at || 0);
      const bDate = new Date(b.created_at || 0);
      return bDate.getTime() - aDate.getTime();
    });
  };

  const columns = [
    {
      title: 'Role Name',
      dataIndex: 'job_title_pl__c',
      key: 'id',
      render: (_: any, record: Job) => (
        <Link to={`/job/${record.id}`} onClick={saveCurrentState}>
          {record.position_info?.roleName || 'N/A'}
        </Link>
      )
    },
    {
      title: 'Position Name',
      key: 'positionName',
      render: (_: any, record: Job) => record.position_info?.positionName || 'not_provided'
    },
    {
      title: 'Client Name',
      key: 'clientName',
      render: (_: any, record: Job) => record.position_info?.clientName || 'not_provided'
    },
    {
      title: 'Location',
      key: 'location',
      render: (_: any, record: Job) => {
        if (record.position_info?.positionAllocations?.length > 0) {
          return record.position_info.positionAllocations.map((loc) => loc.Name).join(', ');
        }
        return 'not_provided';
      }
    },
    {
      title: 'Order Type',
      key: 'orderType',
      render: (_: any, record: Job) => record.position_info?.positionTypeName || 'not_provided'
    },
    {
      title: 'Created Date',
      key: 'createdDate',
      render: (_: any, record: Job) => (record?.created_at ? formatDateTime(record.created_at) : 'not_provided')
    },
    {
      title: 'Stage',
      key: 'stage',
      render: (_: any, record: Job) => {
        const reason = record.position_info?.reasonStatus?.reason;
        const color = reason ? 'red' : 'green';
        return <Tag color={color}>{reason || 'Open'}</Tag>;
      }
    }
  ];

  return (
    <>
      <Row style={{ marginBottom: '24px' }}>
        <Title level={3} style={{ margin: 0, fontSize: '28px', fontWeight: '600' }}>
          Job Orders
        </Title>
      </Row>
      <Row
        style={{
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: '12px',
          gap: '16px',
          backgroundColor: '#fff',
          padding: '7px',
          borderRadius: '12px'
        }}
      >
        <div
          style={{
            fontSize: '16px',
            color: '#666',
            fontWeight: '500'
          }}
        >
          {data?.total_items || 0} Job Orders Listed
        </div>
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '12px'
          }}
        >
          <Search
            style={{
              width: '400px',
              maxWidth: '400px'
            }}
            id='job-orders-search-input'
            onChange={(e) => setSearchTerm(e.target.value)}
            onSearch={setSearchTerm}
            placeholder='Search Positions'
            value={searchTerm || ''}
            enterButton
          />
          <TableFilterController
            filterType='job'
            filters={filters}
            onFiltersChange={handleFiltersChange}
            onClearFilters={handleClearFilters}
            onApplyFilters={handleApplyFilters}
            availableOptions={filterOptions}
            loading={filterOptionsLoading}
          />
        </div>
      </Row>
      <Table
        rowKey='id'
        loading={loading}
        columns={columns}
        dataSource={getSortedData(data?.items)}
        onRow={(record) => ({
          onClick: () => handleRowClick(record),
          style: { cursor: 'pointer', backgroundColor: '#fff', borderBottom: '1px solid #f0f0f0' }
        })}
        pagination={{
          current: page,
          pageSize: limit,
          total: data?.total_items || 0,
          onChange: (newPage, newLimit) => {
            setPage(newPage);
            setLimit(newLimit);
          },
          showSizeChanger: true,
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
        }}
      />
    </>
  );
};
