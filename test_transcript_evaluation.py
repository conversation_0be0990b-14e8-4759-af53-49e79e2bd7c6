#!/usr/bin/env python3
"""
Test script to verify transcript-based evaluation functionality.
"""

import sys
import os
sys.path.append('smarthr-be')

try:
    from models.interview import TranscriptQuestions, TranscriptQuestion
    print("✅ Successfully imported TranscriptQuestions and TranscriptQuestion models")
except ImportError as e:
    print(f"❌ Failed to import models: {e}")
    sys.exit(1)

def test_transcript_question_model():
    """Test that our new models work correctly."""
    print("Testing TranscriptQuestion model...")
    
    # Test creating a single question
    question = TranscriptQuestion(
        question_number=1,
        question_text="Can you explain your experience with Salesforce?",
        category="Technical Skills"
    )
    print(f"Created question: {question}")
    
    # Test creating multiple questions
    questions = TranscriptQuestions(
        questions=[
            TranscriptQuestion(
                question_number=1,
                question_text="Can you explain your experience with Salesforce?",
                category="Technical Skills"
            ),
            TranscriptQuestion(
                question_number=2,
                question_text="What testing methodologies are you familiar with?",
                category="Methodologies"
            )
        ]
    )
    print(f"Created questions collection: {questions}")
    print(f"Number of questions: {len(questions.questions)}")
    
    # Test model_dump
    questions_dict = questions.model_dump()
    print(f"Questions as dict: {questions_dict}")
    
    print("✅ TranscriptQuestion models work correctly!")
    return True

def test_sample_transcript():
    """Test with a sample transcript to see what questions would be identified."""
    sample_transcript = """
TECHNICAL INTERVIEW TRANSCRIPT
Date: September 3, 2025
Interviewer: Santiago
Candidate: Test Candidate

INTERVIEWER: Good morning! Let's start with some technical questions about your Salesforce experience.

TECHNICAL SKILLS
1. Can you explain your experience with Salesforce configurations and customizations?

CANDIDATE: I have about 2 years of experience working with Salesforce. I've done some basic configurations like creating custom fields and setting up workflows.

METHODOLOGIES
2. What testing methodologies are you familiar with, and how have you applied them?

CANDIDATE: I'm familiar with Agile methodology from my previous job. We had 2-week sprints and daily standups.

TECHNICAL SKILLS
3. How do you approach API testing in Salesforce applications?

CANDIDATE: I have experience with API testing using Postman. I've tested REST APIs and know how to check response codes.
"""
    
    print("\nTesting with sample transcript...")
    print(f"Transcript length: {len(sample_transcript)} characters")
    
    # Count expected questions manually
    expected_questions = [
        "Can you explain your experience with Salesforce configurations and customizations?",
        "What testing methodologies are you familiar with, and how have you applied them?",
        "How do you approach API testing in Salesforce applications?"
    ]
    
    print(f"Expected to find {len(expected_questions)} questions:")
    for i, q in enumerate(expected_questions, 1):
        print(f"  {i}. {q}")
    
    print("✅ Sample transcript analysis complete!")
    return True

if __name__ == "__main__":
    print("🧪 Testing Transcript-Based Evaluation Components")
    print("=" * 50)
    
    try:
        # Test 1: Model functionality
        test_transcript_question_model()
        print()
        
        # Test 2: Sample transcript analysis
        test_sample_transcript()
        print()
        
        print("🎉 All tests passed! The transcript-based evaluation components are working correctly.")
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
