from geopy.distance import geodesic
from models.llm import models_pool
from models.output import OutputLocationList

# Ejemplo de filtro de ubicación basado en latitud y longitud
def filter_by_location(candidate_lat, candidate_long, job_lat, job_long, max_distance_km):
    if geodesic((candidate_lat, candidate_long), (job_lat, job_long)).km <= max_distance_km:
        return True
    return False

def distance_with_job(candidate_lat, candidate_long, job_lat, job_long):
    return geodesic((candidate_lat, candidate_long), (job_lat, job_long)).km


# Ejemplo de filtro de salario deseado
def filter_by_salary(candidate_pay_rate, job_salary, tolerance=0.1):
    # Permite una tolerancia del 10% (o configurable)
    lower_bound = job_salary * (1 - tolerance)
    upper_bound = job_salary * (1 + tolerance)
    if lower_bound <= candidate_pay_rate <= upper_bound:
        return True
    return False

# Ejemplo de filtro de habilidades (intersección de habilidades requeridas con las del candidato)
def filter_by_skills(candidate_skills, job_skills):
    candidate_skill_set = set(candidate_skills.split(','))
    job_skill_set = set(job_skills.split(','))
    if len(candidate_skill_set.intersection(job_skill_set)) > 0:
        return True
    return False




# Filtro de ubicación basado en dirección
# from geopy.geocoders import Nominatim
# from geopy.extra.rate_limiter import RateLimiter
# from haversine import haversine, Unit

# # Inicializa el geocodificador
# geolocator = Nominatim(user_agent="Geopy Library")
# geocode = RateLimiter(geolocator.geocode, min_delay_seconds=0.01)
    

# def is_within_range(location_text, candidate_lat, candidate_lon, max_miles):
#     """
#     Verifica si la posición en 'location_text' está dentro del rango de millas desde las coordenadas del candidato.
    
#     Parámetros:
#     - location_text (str): Dirección de la posición en formato texto.
#     - candidate_lat (float): Latitud del candidato.
#     - candidate_lon (float): Longitud del candidato.
#     - max_miles (float): Millas máximas que el candidato está dispuesto a moverse.
    
#     Retorno:
#     - bool: True si la posición está dentro del rango, False si no lo está.
#     - float: Distancia en millas si está dentro del rango, None si hay error.
#     """
#     if (location_text is None):
#         return False, "Location text is not specified"
#     if (candidate_lat is None) or (candidate_lon is None):
#         return False, "Candidate latitude or longitude is not specified"
#     if (max_miles is None):
#         return False, "Max miles is not specified"
    

#     try:
#         # Geocodifica la dirección en texto para obtener latitud y longitud
#         #location = geolocator.geocode(location_text)
#         location = geocode(location_text)
#         if location is None:
#             structured_llm = models_pool["gpt-4o-mini"].with_structured_output(OutputLocationList,method="json_mode")
#             system_prompt = """
#             You will be a location parser model. Your input will be a location in text format. Due to the library limitations sometimes the full string not return anything. 
#             So you need to desagregate the input and propose some combination of the input.
#             Example:
#             Input: "1500 Solana Boulevard #1100 Westlake, TX 76262 US"
#             Output: 
#             {"location_options": ["1500 Solana Boulevard #1100", "Westlake", "TX 76262 US"]}

#             Your output will be a json with the following fields:
#             class OutputLocationList (BaseModel):
#                 location_options : list = Field([], description="List of locations based on original location")
#             """
#             messages =[ 
#                     {"role": "system", "content": system_prompt},
#                     {"role": "user", "content": f"Input: {location_text}"},
#             ]
#             locations = structured_llm.invoke(messages)
#             print(locations)
#             locations = dict(locations)
#             for location in locations["location_options"]:
#                 location = geocode(location)
#                 if location is not None:
#                     break 
#         if location is None:
#             return False, "Location not found"


#         position_lat, position_lon = location.latitude, location.longitude

#         # Calcula la distancia en millas
#         candidate_location = (candidate_lat, candidate_lon)
#         position_location = (position_lat, position_lon)
#         distance = haversine(candidate_location, position_location, unit=Unit.MILES)

#         # Verifica si la distancia está dentro del rango permitido
#         if distance <= max_miles:
#             return True, "Distance: " + str(round(distance,3)) + " miles"
#         else:
#             return False, "Total distance exceeds max allowed miles (" + str(max_miles) + "): " + str(round(distance,3)) + " miles"

#     except Exception as e:
#         # Manejo de errores en caso de fallas en la geocodificación
#         print(f"Error al procesar la ubicación: {e}")
#         return False, "Error in location processing"


from haversine import haversine, Unit

# Replace with your Azure Maps subscription key
subscription_key = "3wPttY0oMKzDBLIoJgLx8WPpCuPMVEE28fLC1Hd1hnt2y0PfVLPIJQQJ99AJACYeBjFNaCgDAAAgAZMP32El"

from functools import lru_cache
import requests

# Cache function with maxsize (number of items to store) set to 1000
@lru_cache(maxsize=1000)
def geocode_location_azure_cached(location_text):
    # Azure Maps API call
    url = f"https://atlas.microsoft.com/search/address/json"
    params = {
        "api-version": "1.0",
        "subscription-key": subscription_key,
        "query": location_text
    }
    response = requests.get(url, params=params)
    if response.status_code == 200:
        data = response.json()
        if 'results' in data and len(data['results']) > 0:
            position = data['results'][0]['position']
            return position['lat'], position['lon']
    return None

def is_within_range(location_text, candidate_lat, candidate_lon, max_miles):
    """
    Verifies if 'location_text' is within max miles from candidate coordinates.
    
    Parameters:
    - location_text (str): Text address of the location.
    - candidate_lat (float): Latitude of the candidate.
    - candidate_lon (float): Longitude of the candidate.
    - max_miles (float): Max miles the candidate is willing to travel.
    
    Returns:
    - bool: True if within range, False otherwise.
    - str: Distance information or error message.
    """
    if not location_text:
        return False, "Location text is not specified"
    if candidate_lat is None or candidate_lon is None:
        return False, "Candidate latitude or longitude is not specified"
    if max_miles is None:
        return False, "Max miles is not specified"
    
    try:
        # Geocode location using Azure Maps
        location = geocode_location_azure(location_text)
        if location is None:
            return False, "Location not found"
        
        position_lat, position_lon = location

        # Calculate the distance in miles
        candidate_location = (candidate_lat, candidate_lon)
        position_location = (position_lat, position_lon)
        distance = haversine(candidate_location, position_location, unit=Unit.MILES)

        # Check if the distance is within the allowed range
        if distance <= max_miles:
            return True, f"Distance: {round(distance, 3)} miles"
        else:
            return False, f"Distance exceeds max allowed ({max_miles}): {round(distance, 3)} miles"

    except Exception as e:
        # Handle errors in geocoding
        print(f"Error processing location: {e}")
        return False, "Error in location processing"