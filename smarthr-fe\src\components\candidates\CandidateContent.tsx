import React from 'react';
import { Col, Row, Typography, Card } from 'antd';
import { DescriptionItem } from '../common/DescriptionItem';
import type { CandidateInfo, Candidate } from '../../types/candidate';
import { formatDateTime, hasValue, isValidUrl } from '../../utils/dateUtils';

const { Text, Title } = Typography;

interface CandidateContentProps {
  candidateInfo: CandidateInfo;
  createdAt: string;
  candidate?: Candidate;
}

export const CandidateContent: React.FC<CandidateContentProps> = ({ candidateInfo, createdAt, candidate }) => {
  // Helper function to render only meaningful fields
  const renderField = (title: string, content: any, isUrl = false) => {
    if (!hasValue(content)) return null;
    
    if (isUrl && isValidUrl(content)) {
      const href = content.startsWith('http') ? content : `https://${content}`;
      return (
        <DescriptionItem
          title={title}
          content={
            <a href={href} target='_blank' rel='noopener noreferrer'>
              {content}
            </a>
          }
        />
      );
    }
    
    return <DescriptionItem title={title} content={content} />;
  };

  return (
    <div>
      {/* Summary Section */}
      {hasValue(candidateInfo.summary) && (
        <div style={{ marginBottom: 32 }}>
          <Title level={4} style={{ marginBottom: 16, marginTop: 0, color: '#333', fontWeight: 600 }}>
            Summary
          </Title>
          <Card style={{ 
            backgroundColor: '#E6EFFF', 
            border: '1px solid #e2e8f0',
            borderRadius: 7 
          }}>
            <Text style={{ 
              fontSize: 14, 
              lineHeight: 1.6, 
              color: '#374151',
              display: 'block'
            }}>
              {candidateInfo.summary}
            </Text>
          </Card>
        </div>
      )}

      {/* Experience Section */}
      {hasValue(candidateInfo.work_experience) && candidateInfo.work_experience.length > 0 && (
        <div style={{ marginBottom: 32 }}>
          <Title level={4} style={{ marginBottom: 16, color: '#333', fontWeight: 600 }}>
            Experience
          </Title>
          {candidateInfo.work_experience.map((item, index) => (
            <div key={index} style={{ marginBottom: 24 }}>
              <Row>
                <Col span={16}>
                  {renderField('Company', item.company_name)}
                  {renderField('Position', item.job_title)}
                  {renderField('Location', item.location)}
                </Col>
                <Col span={4}>
                  {renderField('Start Date', item.start_date)}
                </Col>
                <Col span={4}>
                  {renderField('End Date', item.end_date)}
                </Col>
              </Row>
              {hasValue(item.responsibilities) && (
                <Row>
                  <Col span={24}>
                    {item.responsibilities?.map((responsibility, idx) => (
                      <DescriptionItem key={idx} content={responsibility} />
                    ))}
                  </Col>
                </Row>
              )}
              {hasValue(item.skills) && (
                <DescriptionItem
                  title='Skills'
                  content={item.skills?.map((x) => x.name).join(', ')}
                />
              )}
            </div>
          ))}
        </div>
      )}

      {/* Education Section */}
      {hasValue(candidateInfo.education) && candidateInfo.education.length > 0 && (
        <div style={{ marginBottom: 32 }}>
          <Title level={4} style={{ marginBottom: 16, color: '#333', fontWeight: 600 }}>
            Education
          </Title>
          {candidateInfo.education.map((item, index) => (
            <Row key={index} style={{ marginBottom: 24 }}>
              <Col span={16}>
                {renderField('Institution', item.institution_name)}
                {renderField('Degree', item.degree)}
                {renderField('Location', item.location)}
              </Col>
              <Col span={4}>
                {renderField('Start Date', item.start_date)}
              </Col>
              <Col span={4}>
                {renderField('End Date', item.end_date)}
              </Col>
            </Row>
          ))}
        </div>
      )}

      {/* Certifications Section */}
      {hasValue(candidateInfo.certifications) && candidateInfo.certifications.length > 0 && (
        <div style={{ marginBottom: 32 }}>
          <Title level={4} style={{ marginBottom: 16, color: '#333', fontWeight: 600 }}>
            Certifications
          </Title>
          {candidateInfo.certifications.map((item, index) => (
            <Row key={index} style={{ marginBottom: 24 }}>
              <Col span={18}>
                {renderField('Certification Name', item.name)}
                {hasValue(item.skills) && (
                  <DescriptionItem
                    title='Skills'
                    content={item.skills?.join(', ')}
                  />
                )}
              </Col>
              <Col span={6}>
                {renderField('Issue Date', item.issue_date)}
              </Col>
            </Row>
          ))}
        </div>
      )}

      {/* Technical Skills Section */}
      {hasValue(candidateInfo.skills) && candidateInfo.skills.length > 0 && (
        <div style={{ marginBottom: 32 }}>
          <Title level={4} style={{ marginBottom: 16, color: '#333', fontWeight: 600 }}>
            Technical Skills
          </Title>
          <DescriptionItem
            content={candidateInfo.skills.map((x) => x.name).join(', ')}
          />
        </div>
      )}

      {/* Soft Skills Section */}
      {hasValue(candidateInfo.soft_skills) && candidateInfo.soft_skills!.length > 0 && (
        <div style={{ marginBottom: 32 }}>
          <Title level={4} style={{ marginBottom: 16, color: '#333', fontWeight: 600 }}>
            Soft Skills
          </Title>
          <DescriptionItem
            content={candidateInfo.soft_skills!.map((x) => x.name).join(', ')}
          />
        </div>
      )}

      

      
    </div>
  );
};
