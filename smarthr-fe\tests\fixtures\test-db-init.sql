-- Test database initialization script
-- This script sets up the basic schema and test data for E2E tests

-- Create test users table if it doesn't exist
CREATE TABLE IF NOT EXISTS test_users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL DEFAULT 'user',
    display_name VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert test users
INSERT INTO test_users (email, password_hash, role, display_name) VALUES
('<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VjPyS1Uy6', 'admin', 'Test Admin'),
('<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VjPyS1Uy6', 'recruiter', 'Test Recruiter'),
('<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VjPyS1Uy6', 'interviewer', 'Test Interviewer')
ON CONFLICT (email) DO NOTHING;

-- Create test positions table if it doesn't exist
CREATE TABLE IF NOT EXISTS test_positions (
    id SERIAL PRIMARY KEY,
    position_name VARCHAR(255) NOT NULL,
    client_name VARCHAR(255) NOT NULL,
    seniority VARCHAR(50) NOT NULL,
    description TEXT,
    requirements TEXT,
    status VARCHAR(50) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert test positions
INSERT INTO test_positions (position_name, client_name, seniority, description, requirements) VALUES
('Senior Software Engineer', 'TechCorp Inc', 'senior', 'We are looking for a senior software engineer...', 'React, Node.js, PostgreSQL'),
('Frontend Developer', 'StartupXYZ', 'mid', 'Join our frontend team...', 'React, TypeScript, CSS'),
('Full Stack Developer', 'Enterprise Solutions', 'senior', 'Full stack development role...', 'React, Python, AWS'),
('Junior Developer', 'Learning Company', 'junior', 'Great opportunity for junior developers...', 'JavaScript, HTML, CSS')
ON CONFLICT DO NOTHING;

-- Create test candidates table if it doesn't exist
CREATE TABLE IF NOT EXISTS test_candidates (
    id SERIAL PRIMARY KEY,
    first_name VARCHAR(255) NOT NULL,
    last_name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(50),
    location VARCHAR(255),
    skills TEXT,
    experience_years INTEGER,
    resume_url VARCHAR(500),
    status VARCHAR(50) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert test candidates
INSERT INTO test_candidates (first_name, last_name, email, phone, location, skills, experience_years) VALUES
('Alice', 'Johnson', '<EMAIL>', '******-0101', 'San Francisco', 'React, Node.js, PostgreSQL', 5),
('Bob', 'Smith', '<EMAIL>', '******-0102', 'New York', 'Python, Django, MySQL', 3),
('Carol', 'Davis', '<EMAIL>', '******-0103', 'Austin', 'JavaScript, Vue.js, MongoDB', 4),
('David', 'Wilson', '<EMAIL>', '******-0104', 'Seattle', 'Java, Spring, Oracle', 7)
ON CONFLICT (email) DO NOTHING;

-- Create test interviews table if it doesn't exist
CREATE TABLE IF NOT EXISTS test_interviews (
    id SERIAL PRIMARY KEY,
    position_id INTEGER REFERENCES test_positions(id),
    candidate_id INTEGER REFERENCES test_candidates(id),
    interview_type VARCHAR(50) NOT NULL,
    scheduled_at TIMESTAMP,
    status VARCHAR(50) DEFAULT 'scheduled',
    notes TEXT,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create test interview questions table if it doesn't exist
CREATE TABLE IF NOT EXISTS test_interview_questions (
    id SERIAL PRIMARY KEY,
    position_id INTEGER REFERENCES test_positions(id),
    question_text TEXT NOT NULL,
    question_type VARCHAR(50) NOT NULL,
    difficulty VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert sample interview questions
INSERT INTO test_interview_questions (position_id, question_text, question_type, difficulty) VALUES
(1, 'Explain the difference between React functional and class components.', 'technical', 'medium'),
(1, 'How do you handle state management in a large React application?', 'technical', 'hard'),
(1, 'Describe a challenging project you worked on and how you overcame obstacles.', 'behavioral', 'medium'),
(2, 'What are the benefits of using TypeScript over JavaScript?', 'technical', 'medium'),
(2, 'How do you ensure cross-browser compatibility in your frontend code?', 'technical', 'medium'),
(3, 'Explain the concept of RESTful APIs and how you would design one.', 'technical', 'hard'),
(4, 'What is the difference between let, const, and var in JavaScript?', 'technical', 'easy')
ON CONFLICT DO NOTHING;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_test_positions_status ON test_positions(status);
CREATE INDEX IF NOT EXISTS idx_test_candidates_status ON test_candidates(status);
CREATE INDEX IF NOT EXISTS idx_test_interviews_position_id ON test_interviews(position_id);
CREATE INDEX IF NOT EXISTS idx_test_interviews_candidate_id ON test_interviews(candidate_id);
CREATE INDEX IF NOT EXISTS idx_test_interview_questions_position_id ON test_interview_questions(position_id);

-- Grant permissions (adjust as needed for your application user)
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO your_app_user;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO your_app_user;
