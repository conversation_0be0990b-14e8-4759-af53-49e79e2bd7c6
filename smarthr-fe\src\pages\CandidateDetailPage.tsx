import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { <PERSON>, Col, Card, Tabs, <PERSON>, Button } from 'antd';
import {
  MailOutlined,
  PhoneOutlined,
  EnvironmentOutlined,
  GithubOutlined,
  GlobalOutlined,
  LinkedinOutlined,
  ArrowLeftOutlined
} from '@ant-design/icons';
import { CandidateContent } from '../components/candidates/CandidateContent';
import { Candidate } from '../types/candidate';
import { api, endpoints } from '../utils/api';
import { CandidateFeedback } from '../components/candidates/CandidateFeedback';
import { CandidateNotes } from '../components/candidates/CandidateNotes';
import { useFetch } from '../hooks/useFetch';
import { useNavigation } from '../contexts/NavigationContext';
import { Breadcrumbs } from '../components/layout/Breadcrumbs';

interface FeedbackItem {
  id: string;
  position_id: string;
  candidate_id: string;
  candidate_info: any;
  position_info: any;
  created_at: string;
  updated_at: string;
  feedback_hr: any;
  interview_date_hr: string;
  feedback_date_hr: string;
  status_hr: string;
  recommendation_hr: boolean;
  transcript_hr: string;
  feedback_tec: any;
  interview_date_tec: string;
  feedback_date_tec: string;
  status_tec: string;
  recommendation_tec: boolean;
  transcript_tec: string;
}

export const CandidateDetailPage: React.FC = () => {
  const { id } = useParams();
  const { navigateBack } = useNavigation();
  const [candidate, setCandidate] = useState<Candidate | undefined>(undefined);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('resume');

  // Feedback state
  const [feedbackLoading, setFeedbackLoading] = useState(false);
  const [feedback, setFeedback] = useState<FeedbackItem[]>([]);
  const [selectedFeedback, setSelectedFeedback] = useState<FeedbackItem | null>(null);
  const [drawerOpen, setDrawerOpen] = useState(false);

  const { data: candidateData, loading: fetchLoading } = useFetch<Candidate>({
    url: endpoints.candidates.get(id!),
    dependencies: [id]
  });

  useEffect(() => {
    if (candidateData) {
      setCandidate(candidateData);
    } else {
      setLoading(true);
    }
  }, [candidateData]);

  useEffect(() => {
    if (activeTab === 'feedback') {
      setFeedbackLoading(true);
      api
        .get(`/interview/?candidate_id=${id}`)
        .then((res) => setFeedback(res.data))
        .catch(() => setFeedback([]))
        .finally(() => setFeedbackLoading(false));
    }
  }, [activeTab, id]);

  if (fetchLoading || !candidate) return <Spin style={{ margin: '2rem auto', display: 'block' }} />;

  const info = candidate.candidate_info;

  // Define items for Tabs
  const tabItems = [
    {
      key: 'resume',
      label: 'Resume',
      children: (
        <CandidateContent
          candidateInfo={info}
          createdAt={candidate.created_at?.substring(0, 10) ?? 'N/A'}
          candidate={candidate}
        />
      )
    },
    {
      key: 'notes',
      label: 'Notes',
      children: <CandidateNotes candidateId={candidate.id} candidateInfo={info} />
    },
    {
      key: 'feedback',
      label: 'Feedback',
      children: <CandidateFeedback candidateId={candidate.id} candidateInfo={info} />
    }
  ];

  return (
    <div style={{ padding: '24px', background: '#f5f7fa', minHeight: '100vh' }}>
      {/* Breadcrumbs and Back Button aligned horizontally */}
      <Row
        align='middle'
        style={{
          marginBottom: '24px',
          padding: '0'
        }}
      >
        <Col>
          <Breadcrumbs />
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={6}>
          <Card
            style={{
              borderRadius: 12,
              border: 'none',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)'
            }}
          >
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: 24 }}>
              <div
                style={{
                  width: 60,
                  height: 60,
                  borderRadius: '50%',
                  background: '#6366f1',
                  color: '#fff',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: 24,
                  fontWeight: 600,
                  marginRight: 16,
                  flexShrink: 0
                }}
              >
                {(() => {
                  const name = info.personal_info?.full_name || '';
                  const parts = name.trim().split(' ');
                  if (parts.length === 0 || !parts[0]) return '?';
                  if (parts.length === 1) return parts[0][0].toUpperCase();
                  return (parts[0][0] + parts[parts.length - 1][0]).toUpperCase();
                })()}
              </div>
              <div style={{ flex: 1, minWidth: 0 }}>
                <h3 style={{ marginBottom: 4, fontSize: 18, fontWeight: 600, margin: 0 }}>
                  {info.personal_info?.full_name}
                </h3>
                <p style={{ color: '#6366f1', fontSize: 14, margin: 0 }}>
                  {info.roles && info.roles.length > 0 ? info.roles[0] : 'Professional'}
                </p>
              </div>
            </div>

            <div style={{ marginBottom: 24 }}>
              <h4 style={{ fontSize: 14, fontWeight: 600, marginBottom: 12, color: '#333' }}>Personal information</h4>
              {info.personal_info?.email &&
                info.personal_info.email.split(';').map((email: string, idx: number) => (
                  <div key={idx} style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
                    <MailOutlined style={{ fontSize: 14, color: '#666', marginRight: 8, width: 16 }} />
                    <span style={{ fontSize: 14, color: '#666' }}>{email.trim()}</span>
                  </div>
                ))}
              {info.personal_info?.phone_number && (
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
                  <PhoneOutlined style={{ fontSize: 14, color: '#666', marginRight: 8, width: 16 }} />
                  <span style={{ fontSize: 14, color: '#666' }}>{info.personal_info.phone_number}</span>
                </div>
              )}
              {(info.personal_info?.address || info.personal_info?.city || info.personal_info?.country) && (
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
                  <EnvironmentOutlined style={{ fontSize: 14, color: '#666', marginRight: 8, width: 16 }} />
                  <span style={{ fontSize: 14, color: '#666' }}>
                    {[info.personal_info?.address, info.personal_info?.city, info.personal_info?.country]
                      .filter(Boolean)
                      .join(', ')}
                  </span>
                </div>
              )}
            </div>

            {(info.personal_info?.linkedin_profile || info.personal_info?.website || info.personal_info?.github) && (
              <div style={{ marginBottom: 24 }}>
                <h4 style={{ fontSize: 14, fontWeight: 600, marginBottom: 12, color: '#333' }}>Socials</h4>
                {info.personal_info?.linkedin_profile && (
                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
                    <LinkedinOutlined style={{ fontSize: 14, color: '#666', marginRight: 8, width: 16 }} />
                    <a
                      href={info.personal_info.linkedin_profile}
                      target='_blank'
                      rel='noopener noreferrer'
                      style={{ fontSize: 14, color: '#6366f1', textDecoration: 'none' }}
                    >
                      LinkedIn Profile
                    </a>
                  </div>
                )}
                {info.personal_info?.github && (
                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
                    <GithubOutlined style={{ fontSize: 14, color: '#666', marginRight: 8, width: 16 }} />
                    <a
                      href={info.personal_info.github}
                      target='_blank'
                      rel='noopener noreferrer'
                      style={{ fontSize: 14, color: '#6366f1', textDecoration: 'none' }}
                    >
                      GitHub Profile
                    </a>
                  </div>
                )}
                {info.personal_info?.website && (
                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
                    <GlobalOutlined style={{ fontSize: 14, color: '#666', marginRight: 8, width: 16 }} />
                    <a
                      href={info.personal_info.website}
                      target='_blank'
                      rel='noopener noreferrer'
                      style={{ fontSize: 14, color: '#6366f1', textDecoration: 'none' }}
                    >
                      Website
                    </a>
                  </div>
                )}
              </div>
            )}

            {info.languages && info.languages.length > 0 && (
              <div>
                <h4 style={{ fontSize: 14, fontWeight: 600, marginBottom: 12, color: '#333' }}>Languages</h4>
                {info.languages.map((lang, index) => (
                  <div key={index} style={{ marginBottom: 8 }}>
                    <span style={{ fontSize: 14, color: '#666' }}>{lang.language}</span>
                  </div>
                ))}
              </div>
            )}
          </Card>

          <Card
            style={{
              borderRadius: 12,
              border: 'none',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
              marginTop: 24,
              display: 'none'
            }}
          >
            <h4 style={{ fontSize: 14, fontWeight: 600, marginBottom: 12, color: '#6366f1', margin: 0 }}>
              Roles based on work experience
            </h4>
            <ul>
              {info?.roles?.map((role, index) => (
                <li key={index} style={{ marginBottom: 8 }}>
                  <span style={{ fontSize: 14, color: '#666' }}>{role}</span>
                </li>
              ))}
            </ul>
          </Card>

          <Card
            style={{
              borderRadius: 12,
              border: 'none',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
              marginTop: 24,
              display: 'none'
            }}
          >
            <h4 style={{ fontSize: 14, fontWeight: 600, marginBottom: 12, color: '#6366f1', margin: 0 }}>
              Audit Trail
            </h4>
            <div style={{ margin: 8 }}>
              <span style={{ fontWeight: 500, color: '#666', minWidth: 100, display: 'inline-block' }}>
                Created by:{' '}
              </span>
              <span style={{ color: '#333' }}>{candidate.created_by ?? 'Not specified'}</span>
            </div>
            <div style={{ margin: 8 }}>
              <span style={{ fontWeight: 500, color: '#666', minWidth: 100, display: 'inline-block' }}>
                Created at:{' '}
              </span>
              <span style={{ color: '#333' }}>
                {candidate.created_at ? candidate.created_at.substring(0, 19).replace('T', ' ') : 'Not specified'}
              </span>
            </div>
            <div style={{ margin: 8 }}>
              <span style={{ fontWeight: 500, color: '#666', minWidth: 100, display: 'inline-block' }}>
                Modified by:{' '}
              </span>
              <span style={{ color: '#333' }}>{candidate.updated_by ?? 'Not specified'}</span>
            </div>
            <div style={{ margin: 8 }}>
              <span style={{ fontWeight: 500, color: '#666', minWidth: 100, display: 'inline-block' }}>
                Modified at:{' '}
              </span>
              <span style={{ color: '#333' }}>
                {candidate.updated_at ? candidate.updated_at.substring(0, 19).replace('T', ' ') : 'Not specified'}
              </span>
            </div>
          </Card>
        </Col>
        <Col span={18}>
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            items={tabItems}
            type='card'
            style={
              {
                '--ant-primary-color': '#6c63ff'
              } as React.CSSProperties
            }
            className='candidate-detail-tabs'
          />
        </Col>
      </Row>
    </div>
  );
};
