import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from './BasePage';
import { waitForApiCalls } from '../utils/test-helpers';

/**
 * Page Object Model for the Job Details page (/job/:id)
 */
export class JobDetailsPage extends BasePage {
  private jobId: string;

  constructor(page: Page, jobId?: string) {
    super(page);
    this.jobId = jobId || '';
  }

  getUrl(): string {
    return `/job/${this.jobId}`;
  }

  async waitForPageLoad(): Promise<void> {
    await this.page.waitForSelector('.ant-card, [data-testid="job-details"]', { timeout: 30000 });
    await this.waitForLoading();
  }

  /**
   * Navigation tabs
   */
  get descriptionTab() {
    return this.page.locator('.ant-tabs-tab:has-text("Description"), [data-testid="description-tab"]');
  }

  get matchingsTab() {
    return this.page.locator('.ant-tabs-tab:has-text("Matchings"), [data-testid="matchings-tab"]');
  }

  get manualMatchingTab() {
    return this.page.locator('.ant-tabs-tab:has-text("Manual"), [data-testid="manual-matching-tab"]');
  }

  get interviewTab() {
    return this.page.locator('.ant-tabs-tab:has-text("Interview"), [data-testid="interview-tab"]');
  }

  get candidatesTab() {
    return this.page.locator('.ant-tabs-tab:has-text("Candidates"), [data-testid="candidates-tab"]');
  }

  /**
   * Job description elements
   */
  get jobTitle() {
    return this.page.locator('h1, .ant-typography-title, [data-testid="job-title"]');
  }

  get jobDescription() {
    return this.page.locator('[data-testid="job-description"], .job-description');
  }

  get jobRequirements() {
    return this.page.locator('[data-testid="job-requirements"], .job-requirements');
  }

  /**
   * Interview generation elements
   */
  get generateQuestionsButton() {
    return this.page.locator('.ant-btn:has-text("Generate"), button:has-text("Generate")');
  }

  get questionCountInput() {
    return this.page.locator('input[type="number"], .ant-input-number input');
  }

  get technicalSkillsCheckbox() {
    return this.page.locator('.ant-checkbox:has-text("Technical"), input[type="checkbox"] + span:has-text("Technical")');
  }

  get softSkillsCheckbox() {
    return this.page.locator('.ant-checkbox:has-text("Soft"), input[type="checkbox"] + span:has-text("Soft")');
  }

  get methodologiesCheckbox() {
    return this.page.locator('.ant-checkbox:has-text("Methodologies"), input[type="checkbox"] + span:has-text("Methodologies")');
  }

  get languageToolsCheckbox() {
    return this.page.locator('.ant-checkbox:has-text("Language"), input[type="checkbox"] + span:has-text("Language")');
  }

  get questionsContainer() {
    return this.page.locator('[data-testid="questions-container"], .questions-list');
  }

  /**
   * Candidates section elements
   */
  get candidatesList() {
    return this.page.locator('[data-testid="candidates-list"], .candidates-container');
  }

  get candidateCards() {
    return this.page.locator('.ant-card:has([data-testid="candidate-card"]), .candidate-card');
  }

  get addCandidateButton() {
    return this.page.locator('.ant-btn:has-text("Add Candidate"), button:has-text("Add")');
  }

  /**
   * Actions
   */
  async navigateToTab(tabName: 'description' | 'matchings' | 'manual' | 'interview' | 'candidates') {
    const tabMap = {
      description: this.descriptionTab,
      matchings: this.matchingsTab,
      manual: this.manualMatchingTab,
      interview: this.interviewTab,
      candidates: this.candidatesTab
    };

    const tab = tabMap[tabName];
    await tab.click();
    await waitForApiCalls(this.page);
  }

  async generateInterviewQuestions(options: {
    questionCount?: number;
    technicalSkills?: boolean;
    softSkills?: boolean;
    methodologies?: boolean;
    languageTools?: boolean;
  } = {}) {
    // Navigate to interview tab
    await this.navigateToTab('interview');

    // Set question count if provided
    if (options.questionCount) {
      await this.questionCountInput.fill(options.questionCount.toString());
    }

    // Set skill checkboxes
    if (options.technicalSkills !== undefined) {
      const checkbox = this.technicalSkillsCheckbox;
      const isChecked = await checkbox.isChecked();
      if (isChecked !== options.technicalSkills) {
        await checkbox.click();
      }
    }

    if (options.softSkills !== undefined) {
      const checkbox = this.softSkillsCheckbox;
      const isChecked = await checkbox.isChecked();
      if (isChecked !== options.softSkills) {
        await checkbox.click();
      }
    }

    if (options.methodologies !== undefined) {
      const checkbox = this.methodologiesCheckbox;
      const isChecked = await checkbox.isChecked();
      if (isChecked !== options.methodologies) {
        await checkbox.click();
      }
    }

    if (options.languageTools !== undefined) {
      const checkbox = this.languageToolsCheckbox;
      const isChecked = await checkbox.isChecked();
      if (isChecked !== options.languageTools) {
        await checkbox.click();
      }
    }

    // Click generate button
    await this.generateQuestionsButton.click();
    
    // Wait for questions to be generated
    await this.waitForNotification('Questions generated successfully');
    await waitForApiCalls(this.page);
  }

  async viewCandidates() {
    await this.navigateToTab('candidates');
    await this.page.waitForSelector('[data-testid="candidates-list"], .candidates-container');
  }

  async getCandidateCount(): Promise<number> {
    await this.viewCandidates();
    return await this.candidateCards.count();
  }

  async clickCandidate(candidateName: string) {
    const candidateCard = this.page.locator(`.candidate-card:has-text("${candidateName}"), .ant-card:has-text("${candidateName}")`);
    await candidateCard.click();
    await waitForApiCalls(this.page);
  }

  /**
   * Assertions
   */
  async expectJobTitle(expectedTitle: string) {
    await expect(this.jobTitle).toContainText(expectedTitle);
  }

  async expectQuestionsGenerated(minCount = 1) {
    await this.navigateToTab('interview');
    const questions = this.page.locator('.question-item, [data-testid="question"]');
    await expect(questions).toHaveCount({ min: minCount });
  }

  async expectCandidatesVisible(minCount = 1) {
    await this.viewCandidates();
    await expect(this.candidateCards).toHaveCount({ min: minCount });
  }

  async expectTabToBeActive(tabName: string) {
    const activeTab = this.page.locator('.ant-tabs-tab-active');
    await expect(activeTab).toContainText(tabName);
  }

  /**
   * Get generated questions
   */
  async getGeneratedQuestions(): Promise<string[]> {
    await this.navigateToTab('interview');
    const questionElements = this.page.locator('.question-text, [data-testid="question-text"]');
    const count = await questionElements.count();
    const questions: string[] = [];

    for (let i = 0; i < count; i++) {
      const text = await questionElements.nth(i).textContent();
      if (text) {
        questions.push(text.trim());
      }
    }

    return questions;
  }

  /**
   * Get candidate information
   */
  async getCandidateInfo(candidateName: string) {
    await this.viewCandidates();
    const candidateCard = this.page.locator(`.candidate-card:has-text("${candidateName}"), .ant-card:has-text("${candidateName}")`);
    await expect(candidateCard).toBeVisible();
    
    const text = await candidateCard.textContent();
    return text?.trim() || '';
  }
}
