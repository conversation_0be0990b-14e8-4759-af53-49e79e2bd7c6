/**
 * Custom test reporter utilities for SmartHR E2E tests
 * Provides enhanced reporting and test result analysis
 */

import { FullResult, Reporter, Suite, TestCase, TestResult } from '@playwright/test/reporter';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Custom SmartHR test reporter
 */
export class SmartHRReporter implements Reporter {
  private startTime: number = 0;
  private results: TestResult[] = [];
  private outputDir: string;

  constructor(options: { outputDir?: string } = {}) {
    this.outputDir = options.outputDir || 'test-results';
    
    // Ensure output directory exists
    if (!fs.existsSync(this.outputDir)) {
      fs.mkdirSync(this.outputDir, { recursive: true });
    }
  }

  onBegin(config: any, suite: Suite) {
    this.startTime = Date.now();
    console.log(`Starting SmartHR E2E tests with ${config.workers} workers`);
    
    // Log test configuration
    const configSummary = {
      workers: config.workers,
      timeout: config.timeout,
      projects: config.projects.map((p: any) => p.name),
      baseURL: config.use?.baseURL,
      timestamp: new Date().toISOString(),
    };
    
    fs.writeFileSync(
      path.join(this.outputDir, 'config.json'),
      JSON.stringify(configSummary, null, 2)
    );
  }

  onTestEnd(test: TestCase, result: TestResult) {
    this.results.push(result);
    
    // Log test completion
    const status = result.status;
    const duration = result.duration;
    const testTitle = test.title;
    
    console.log(`${status.toUpperCase()}: ${testTitle} (${duration}ms)`);
    
    // Log errors if any
    if (result.errors.length > 0) {
      result.errors.forEach(error => {
        console.error(`  Error: ${error.message}`);
      });
    }
  }

  onEnd(result: FullResult) {
    const endTime = Date.now();
    const duration = endTime - this.startTime;
    
    // Generate test summary
    const summary = this.generateTestSummary(result, duration);
    
    // Write summary to file
    fs.writeFileSync(
      path.join(this.outputDir, 'summary.json'),
      JSON.stringify(summary, null, 2)
    );
    
    // Write detailed results
    fs.writeFileSync(
      path.join(this.outputDir, 'detailed-results.json'),
      JSON.stringify(this.results, null, 2)
    );
    
    // Generate HTML summary
    this.generateHtmlSummary(summary);
    
    // Print summary to console
    this.printSummary(summary);
  }

  private generateTestSummary(result: FullResult, duration: number) {
    const passed = this.results.filter(r => r.status === 'passed').length;
    const failed = this.results.filter(r => r.status === 'failed').length;
    const skipped = this.results.filter(r => r.status === 'skipped').length;
    const flaky = this.results.filter(r => r.status === 'flaky').length;
    const total = this.results.length;
    
    const failedTests = this.results
      .filter(r => r.status === 'failed')
      .map(r => ({
        title: r.test?.title || 'Unknown test',
        error: r.errors[0]?.message || 'Unknown error',
        duration: r.duration,
      }));
    
    const slowestTests = this.results
      .sort((a, b) => b.duration - a.duration)
      .slice(0, 10)
      .map(r => ({
        title: r.test?.title || 'Unknown test',
        duration: r.duration,
        status: r.status,
      }));
    
    return {
      timestamp: new Date().toISOString(),
      duration,
      status: result.status,
      totals: {
        total,
        passed,
        failed,
        skipped,
        flaky,
        passRate: total > 0 ? Math.round((passed / total) * 100) : 0,
      },
      failedTests,
      slowestTests,
      performance: {
        averageTestDuration: total > 0 ? Math.round(this.results.reduce((sum, r) => sum + r.duration, 0) / total) : 0,
        totalTestTime: this.results.reduce((sum, r) => sum + r.duration, 0),
      },
    };
  }

  private generateHtmlSummary(summary: any) {
    const html = `
<!DOCTYPE html>
<html>
<head>
    <title>SmartHR E2E Test Results</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .summary { display: flex; gap: 20px; margin: 20px 0; }
        .metric { background: white; border: 1px solid #ddd; padding: 15px; border-radius: 5px; text-align: center; }
        .metric.passed { border-color: #52c41a; }
        .metric.failed { border-color: #ff4d4f; }
        .metric.skipped { border-color: #faad14; }
        .section { margin: 20px 0; }
        .test-list { background: #f9f9f9; padding: 15px; border-radius: 5px; }
        .test-item { margin: 5px 0; padding: 5px; background: white; border-radius: 3px; }
        .failed { background: #fff2f0; border-left: 3px solid #ff4d4f; }
        .slow { background: #fffbe6; border-left: 3px solid #faad14; }
    </style>
</head>
<body>
    <div class="header">
        <h1>SmartHR E2E Test Results</h1>
        <p>Generated: ${summary.timestamp}</p>
        <p>Duration: ${Math.round(summary.duration / 1000)}s</p>
        <p>Status: <strong>${summary.status}</strong></p>
    </div>
    
    <div class="summary">
        <div class="metric">
            <h3>${summary.totals.total}</h3>
            <p>Total Tests</p>
        </div>
        <div class="metric passed">
            <h3>${summary.totals.passed}</h3>
            <p>Passed</p>
        </div>
        <div class="metric failed">
            <h3>${summary.totals.failed}</h3>
            <p>Failed</p>
        </div>
        <div class="metric skipped">
            <h3>${summary.totals.skipped}</h3>
            <p>Skipped</p>
        </div>
        <div class="metric">
            <h3>${summary.totals.passRate}%</h3>
            <p>Pass Rate</p>
        </div>
    </div>
    
    ${summary.failedTests.length > 0 ? `
    <div class="section">
        <h2>Failed Tests</h2>
        <div class="test-list">
            ${summary.failedTests.map((test: any) => `
                <div class="test-item failed">
                    <strong>${test.title}</strong> (${test.duration}ms)
                    <br><small>${test.error}</small>
                </div>
            `).join('')}
        </div>
    </div>
    ` : ''}
    
    <div class="section">
        <h2>Slowest Tests</h2>
        <div class="test-list">
            ${summary.slowestTests.map((test: any) => `
                <div class="test-item slow">
                    <strong>${test.title}</strong> - ${test.duration}ms (${test.status})
                </div>
            `).join('')}
        </div>
    </div>
    
    <div class="section">
        <h2>Performance Metrics</h2>
        <div class="test-list">
            <div class="test-item">
                <strong>Average Test Duration:</strong> ${summary.performance.averageTestDuration}ms
            </div>
            <div class="test-item">
                <strong>Total Test Time:</strong> ${Math.round(summary.performance.totalTestTime / 1000)}s
            </div>
        </div>
    </div>
</body>
</html>
    `;
    
    fs.writeFileSync(path.join(this.outputDir, 'summary.html'), html);
  }

  private printSummary(summary: any) {
    console.log('\n' + '='.repeat(60));
    console.log('SmartHR E2E Test Results Summary');
    console.log('='.repeat(60));
    console.log(`Status: ${summary.status}`);
    console.log(`Duration: ${Math.round(summary.duration / 1000)}s`);
    console.log(`Total Tests: ${summary.totals.total}`);
    console.log(`Passed: ${summary.totals.passed}`);
    console.log(`Failed: ${summary.totals.failed}`);
    console.log(`Skipped: ${summary.totals.skipped}`);
    console.log(`Pass Rate: ${summary.totals.passRate}%`);
    
    if (summary.failedTests.length > 0) {
      console.log('\nFailed Tests:');
      summary.failedTests.forEach((test: any) => {
        console.log(`  - ${test.title} (${test.duration}ms)`);
        console.log(`    ${test.error}`);
      });
    }
    
    console.log('\nPerformance:');
    console.log(`  Average Test Duration: ${summary.performance.averageTestDuration}ms`);
    console.log(`  Total Test Time: ${Math.round(summary.performance.totalTestTime / 1000)}s`);
    console.log('='.repeat(60));
  }
}

/**
 * Test result analyzer utility
 */
export class TestResultAnalyzer {
  static analyzeResults(resultsPath: string) {
    if (!fs.existsSync(resultsPath)) {
      console.error(`Results file not found: ${resultsPath}`);
      return;
    }
    
    const results = JSON.parse(fs.readFileSync(resultsPath, 'utf8'));
    
    // Analyze test patterns
    const analysis = {
      flakyTests: this.findFlakyTests(results),
      slowTests: this.findSlowTests(results),
      errorPatterns: this.analyzeErrorPatterns(results),
      recommendations: this.generateRecommendations(results),
    };
    
    return analysis;
  }
  
  private static findFlakyTests(results: any[]) {
    // Logic to identify flaky tests based on historical data
    return results.filter(r => r.status === 'flaky');
  }
  
  private static findSlowTests(results: any[]) {
    const averageDuration = results.reduce((sum, r) => sum + r.duration, 0) / results.length;
    const threshold = averageDuration * 2; // Tests taking more than 2x average
    
    return results
      .filter(r => r.duration > threshold)
      .sort((a, b) => b.duration - a.duration);
  }
  
  private static analyzeErrorPatterns(results: any[]) {
    const failedResults = results.filter(r => r.status === 'failed');
    const errorCounts: Record<string, number> = {};
    
    failedResults.forEach(result => {
      result.errors.forEach((error: any) => {
        const errorType = this.categorizeError(error.message);
        errorCounts[errorType] = (errorCounts[errorType] || 0) + 1;
      });
    });
    
    return Object.entries(errorCounts)
      .sort(([, a], [, b]) => b - a)
      .map(([type, count]) => ({ type, count }));
  }
  
  private static categorizeError(errorMessage: string): string {
    if (errorMessage.includes('timeout')) return 'Timeout';
    if (errorMessage.includes('network')) return 'Network';
    if (errorMessage.includes('element not found')) return 'Element Not Found';
    if (errorMessage.includes('navigation')) return 'Navigation';
    return 'Other';
  }
  
  private static generateRecommendations(results: any[]) {
    const recommendations = [];
    
    const failureRate = results.filter(r => r.status === 'failed').length / results.length;
    if (failureRate > 0.1) {
      recommendations.push('High failure rate detected. Consider reviewing test stability.');
    }
    
    const avgDuration = results.reduce((sum, r) => sum + r.duration, 0) / results.length;
    if (avgDuration > 30000) {
      recommendations.push('Tests are running slowly. Consider optimizing test setup and teardown.');
    }
    
    return recommendations;
  }
}
