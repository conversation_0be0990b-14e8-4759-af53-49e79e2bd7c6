export interface BaseFilter {
  searchTerm?: string;
  createdFrom?: string;
  createdTo?: string;
}

export interface CandidateFilter extends BaseFilter {
  status?: 'enabled' | 'disabled' | 'all';
  role?: string;
  country?: string;
  createdBy?: string;
}

export interface JobFilter extends BaseFilter {
  clientName?: string;
  location?: string;
  stage?: 'open' | 'closed' | 'all';
}

export interface FilterOption {
  label: string;
  value: string;
}

export interface DateRange {
  from?: string;
  to?: string;
}

export interface FilterFieldConfig {
  visible?: boolean;
  label?: string;
  placeholder?: string;
}

export interface FilterConfiguration {
  status?: FilterFieldConfig;
  role?: FilterFieldConfig;
  country?: FilterFieldConfig;
  createdBy?: FilterFieldConfig;
  clientName?: FilterFieldConfig;
  location?: FilterFieldConfig;
  stage?: FilterFieldConfig;
  createdFrom?: FilterFieldConfig;
  createdTo?: FilterFieldConfig;
}

export interface FilterControllerProps<T extends BaseFilter> {
  filters: T;
  onFiltersChange: (filters: T) => void;
  onClearFilters: () => void;
  availableOptions?: {
    roles?: FilterOption[];
    countries?: FilterOption[];
    createdBy?: FilterOption[];
    clients?: FilterOption[];
    locations?: FilterOption[];
  };
  loading?: boolean;
} 