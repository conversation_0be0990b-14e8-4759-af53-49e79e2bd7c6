"""
WebSocket routes for real-time notifications
Handles WebSocket connections and provides notification endpoints
"""

import logging
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, HTTPException, Depends, Query
from typing import Optional, List
from utils.websocket_manager import (
    websocket_manager, 
    push_notification, 
    push_system_notification,
    push_candidate_notification,
    push_position_notification,
    push_matching_notification,
    push_interview_notification
)
from pydantic import BaseModel

logger = logging.getLogger(__name__)
router = APIRouter()

# Pydantic models for notification requests


class NotificationRequest(BaseModel):
    user_id: str
    notification_type: str
    payload: dict
    priority: Optional[str] = "normal"


class SystemNotificationRequest(BaseModel):
    message: str
    notification_type: Optional[str] = "system"
    priority: Optional[str] = "normal"


class CandidateNotificationRequest(BaseModel):
    user_id: str
    candidate_id: str
    action: str
    details: Optional[dict] = None


class PositionNotificationRequest(BaseModel):
    user_id: str
    position_id: str
    action: str
    details: Optional[dict] = None


class InterviewNotificationRequest(BaseModel):
    user_id: str
    interview_id: str
    action: str
    details: Optional[dict] = None


class MatchingNotificationRequest(BaseModel):
    user_id: str
    match_results: dict

# WebSocket endpoint for notifications


@router.websocket("/ws/notifications")
async def websocket_notifications(
    websocket: WebSocket, 
    user_id: str = Query(..., description="User ID for the WebSocket connection"),
    client_info: Optional[str] = Query(None, description="Optional client information")
):
    """
    WebSocket endpoint for real-time notifications

    Args:
        websocket: WebSocket connection
        user_id: Unique identifier for the user
        client_info: Optional client information (browser, app version, etc.)
    """
    metadata = {
        "client_info": client_info,
        "endpoint": "/ws/notifications"
    }

    await websocket_manager.register(websocket, user_id, metadata)

    try:
        while True:
            # Keep connection alive and handle ping/pong
            data = await websocket.receive_text()

            # Handle ping/pong or other client messages
            if data == "ping":
                await websocket.send_text("pong")
            elif data == "status":
                await websocket.send_json({
                    "type": "status_response",
                    "connected_users": len(websocket_manager.get_connected_users()),
                    "your_connections": websocket_manager.get_connection_count(user_id),
                    "timestamp": websocket_manager.connection_metadata.get(websocket, {}).get("connected_at")
                })
            else:
                logger.debug(f"Received message from user {user_id}: {data}")

    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected for user {user_id}")
    except Exception as e:
        logger.error(f"WebSocket error for user {user_id}: {str(e)}")
    finally:
        websocket_manager.unregister(websocket, user_id)

# HTTP endpoints for sending notifications (for testing and internal use)


@router.post("/notifications/send")
async def send_notification(request: NotificationRequest):
    """
    Send a custom notification to a specific user

    Args:
        request: Notification request data
    """
    try:
        await push_notification(
            user_id=request.user_id,
            notification_type=request.notification_type,
            payload=request.payload,
            priority=request.priority
        )

        return {
            "success": True,
            "message": f"Notification sent to user {request.user_id}",
            "type": request.notification_type
        }

    except Exception as e:
        logger.error(f"Error sending notification: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to send notification: {str(e)}")


@router.post("/notifications/system")
async def send_system_notification(request: SystemNotificationRequest):
    """
    Send a system-wide notification to all connected users

    Args:
        request: System notification request data
    """
    try:
        await push_system_notification(
            message=request.message,
            notification_type=request.notification_type,
            priority=request.priority
        )

        connected_users = websocket_manager.get_connected_users()

        return {
            "success": True,
            "message": "System notification sent",
            "recipients": len(connected_users),
            "connected_users": connected_users
        }

    except Exception as e:
        logger.error(f"Error sending system notification: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to send system notification: {str(e)}")


@router.post("/notifications/candidate")
async def send_candidate_notification(request: CandidateNotificationRequest):
    """
    Send a candidate-related notification

    Args:
        request: Candidate notification request data
    """
    try:
        await push_candidate_notification(
            user_id=request.user_id,
            candidate_id=request.candidate_id,
            action=request.action,
            details=request.details
        )

        return {
            "success": True,
            "message": f"Candidate notification sent to user {request.user_id}",
            "candidate_id": request.candidate_id,
            "action": request.action
        }

    except Exception as e:
        logger.error(f"Error sending candidate notification: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to send candidate notification: {str(e)}")


@router.post("/notifications/position")
async def send_position_notification(request: PositionNotificationRequest):
    """
    Send a position-related notification

    Args:
        request: Position notification request data
    """
    try:
        await push_position_notification(
            user_id=request.user_id,
            position_id=request.position_id,
            action=request.action,
            details=request.details
        )

        return {
            "success": True,
            "message": f"Position notification sent to user {request.user_id}",
            "position_id": request.position_id,
            "action": request.action
        }

    except Exception as e:
        logger.error(f"Error sending position notification: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to send position notification: {str(e)}")


@router.post("/notifications/interview")
async def send_interview_notification(request: InterviewNotificationRequest):
    """
    Send an interview-related notification

    Args:
        request: Interview notification request data
    """
    try:
        await push_interview_notification(
            user_id=request.user_id,
            interview_id=request.interview_id,
            action=request.action,
            details=request.details
        )

        return {
            "success": True,
            "message": f"Interview notification sent to user {request.user_id}",
            "interview_id": request.interview_id,
            "action": request.action
        }

    except Exception as e:
        logger.error(f"Error sending interview notification: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to send interview notification: {str(e)}")


@router.post("/notifications/matching")
async def send_matching_notification(request: MatchingNotificationRequest):
    """
    Send a matching results notification

    Args:
        request: Matching notification request data
    """
    try:
        await push_matching_notification(
            user_id=request.user_id,
            match_results=request.match_results
        )

        return {
            "success": True,
            "message": f"Matching notification sent to user {request.user_id}",
            "results_count": len(request.match_results.get("matches", []))
        }

    except Exception as e:
        logger.error(f"Error sending matching notification: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to send matching notification: {str(e)}")

# WebSocket management endpoints


@router.get("/websocket/status")
async def get_websocket_status():
    """
    Get current WebSocket connection status
    """
    try:
        connection_info = websocket_manager.get_connection_info()

        return {
            "success": True,
            "websocket_status": "active",
            "connection_info": connection_info
        }

    except Exception as e:
        logger.error(f"Error getting WebSocket status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get WebSocket status: {str(e)}")


@router.get("/websocket/users")
async def get_connected_users():
    """
    Get list of currently connected users
    """
    try:
        connected_users = websocket_manager.get_connected_users()

        return {
            "success": True,
            "connected_users": connected_users,
            "total_users": len(connected_users),
            "total_connections": websocket_manager.get_connection_count()
        }

    except Exception as e:
        logger.error(f"Error getting connected users: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get connected users: {str(e)}")


@router.post("/websocket/test/{user_id}")
async def test_websocket_connection(user_id: str):
    """
    Test WebSocket connection by sending a test message

    Args:
        user_id: User ID to send test message to
    """
    try:
        await push_notification(
            user_id=user_id,
            notification_type="test_message",
            payload={
                "message": "This is a test notification",
                "test": True
            },
            priority="low"
        )

        return {
            "success": True,
            "message": f"Test notification sent to user {user_id}",
            "user_connected": user_id in websocket_manager.get_connected_users()
        }

    except Exception as e:
        logger.error(f"Error testing WebSocket connection: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to test WebSocket connection: {str(e)}")
