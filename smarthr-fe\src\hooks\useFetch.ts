import { useState, useEffect } from 'react';
import { api } from '../utils/api';
import { AxiosError } from 'axios';

interface UseFetchOptions<T> {
  url: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  params?: Record<string, any>;
  body?: any;
  dependencies?: any[];
  skip?: boolean;
}

interface UseFetchReturn<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

export function useFetch<T>({ url, method = 'GET', params, body, dependencies = [], skip = false }: UseFetchOptions<T>): UseFetchReturn<T> {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (skip) return;
    const fetchData = async () => {
      try {
        setLoading(true);
        const response = await api.request({
          url,
          method,
          params,
          data: body
        });
        setData(response.data);
        setError(null);
      } catch (err) {
        const error = err as AxiosError;
        setError(error.message);
        console.error('Fetch error:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, dependencies);

  const refetch = async () => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const response = await api.request({
          url,
          method,
          params,
          data: body
        });
        setData(response.data);
        setError(null);
      } catch (err) {
        const error = err as AxiosError;
        setError(error.message);
        console.error('Fetch error:', error);
      } finally {
        setLoading(false);
      }
    };

    await fetchData();
  };

  return { data, loading, error, refetch };
} 