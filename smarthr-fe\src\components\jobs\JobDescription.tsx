import React from 'react';
import { <PERSON>, <PERSON>, Tag, But<PERSON>, Breadcrumb, Typography, Card, Divider } from 'antd';
import { Link } from 'react-router-dom';
import { useJobContext } from '../../contexts/JobContext';
import { formatDateTime } from '../../utils/dateUtils';

const { Title, Text } = Typography;

const formatDate = (date?: string) => {
  return date?.substring(0, 10) || '01/02/2025 5:00 PM';
};

export const JobDescription: React.FC = () => {
  const { jobData } = useJobContext();

  if (!jobData) {
    return (
      <Card>
        <Breadcrumb
          style={{ marginBottom: 16 }}
          items={[{ title: <Link to='/'>Job Orders</Link> }, { title: 'Job Description' }]}
        />
        <p>
          Job not found. Please return to <Link to='/'>Job Orders</Link> and try again.
        </p>
      </Card>
    );
  }

  const seniority = jobData.position_info?.seniority?.name || 'Seniority Unknown';
  const clientName = jobData.position_info?.clientName || 'Not specified';

  // Format position allocations
  const positionAllocations = jobData.position_info?.positionAllocations || [];
  const locationText =
    positionAllocations.length > 0 ? positionAllocations.map((loc) => loc.Name).join(', ') : 'Remote';

  return (
    <div style={{ background: '#fff', padding: 32, borderRadius: 8, display: 'flex' }}>
      <div style={{ flex: 1, marginLeft: 24 }}>
        {/* Top Section */}
        <Row gutter={48}>
          <Col span={12}>
            <div>
              <Title level={3} style={{ marginBottom: 0 }}>
                {jobData.position_info?.roleName || jobData.position_info?.positionName}
              </Title>
              <Tag color='blue' style={{ marginTop: 8 }}>
                {seniority}
              </Tag>
            </div>

            <div style={{ margin: '16px 0', display: 'flex', alignItems: 'center' }}>
              <span style={{ marginRight: 8 }}>📍</span>
              <Text>{locationText}</Text>
            </div>

            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col>
                <Tag>$ Per hour</Tag>
              </Col>
              <Col>
                <Tag>✓ Full-Time</Tag>
              </Col>
              <Col>
                <Tag>✓ Remote</Tag>
              </Col>
              <Col>
                <Tag>5 Years Experience</Tag>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Text type='secondary'>Client Name</Text>
                <div>{clientName}</div>
              </Col>
              <Col span={12}>
                <Text type='secondary'>Position Type</Text>
                <div>{jobData.position_info?.positionTypeName || 'Not specified'}</div>
              </Col>
            </Row>

            <Row gutter={16} style={{ marginTop: 16 }}>
              <Col span={12}>
                <Text type='secondary'>Start date</Text>
                <div>{formatDate(jobData.position_info?.positionStartDate)}</div>
              </Col>
              <Col span={12}>
                <Text type='secondary'>Close Date</Text>
                <div>{formatDate(jobData.position_info?.positionCloseDate)}</div>
              </Col>
            </Row>

            {/* About the job */}
            <Card style={{ background: '#EAF6FB', marginTop: 24 }} bordered={false}>
              <Title level={5}>About the job</Title>
              <Text>{jobData.position_info?.jobDescription || 'No job description available.'}</Text>
            </Card>

            {/* Benefits */}
            <Card style={{ marginTop: 24 }} bordered={false}>
              <Title level={5}>Benefits</Title>
              <Row gutter={[24, 24]}>
                <Col span={12}>
                  <div style={{ display: 'flex' }}>
                    <div style={{ marginRight: 12, fontSize: 20 }}>🌳</div>
                    <div>
                      <strong>Outdoor areas</strong>
                      <div>The offices have open spaces (parks, terraces, etc.)</div>
                    </div>
                  </div>
                </Col>
                <Col span={12}>
                  <div style={{ display: 'flex' }}>
                    <div style={{ marginRight: 12, fontSize: 20 }}>💻</div>
                    <div>
                      <strong>Computer</strong>
                      <div>TOIT provides a computer for your work.</div>
                    </div>
                  </div>
                </Col>
                <Col span={12}>
                  <div style={{ display: 'flex' }}>
                    <div style={{ marginRight: 12, fontSize: 20 }}>🏖️</div>
                    <div>
                      <strong>Extra vacation</strong>
                      <div>TOIT grants paid vacations in addition to the legal minimum.</div>
                    </div>
                  </div>
                </Col>
                <Col span={12}>
                  <div style={{ display: 'flex' }}>
                    <div style={{ marginRight: 12, fontSize: 20 }}>🌎</div>
                    <div>
                      <strong>Remote Job</strong>
                      <div>The position can be performed from anywhere in the world.</div>
                    </div>
                  </div>
                </Col>
              </Row>
            </Card>

            {/* Metadata */}
            <Card style={{ marginTop: 24 }} bordered={false}>
              <Row gutter={16}>
                <Col span={12}>
                  <Text type='secondary'>Created by</Text>
                  <div>{jobData.position_info?.createdBy || 'Not specified'}</div>
                  <div style={{ marginTop: 8 }}>
                    <Text type='secondary'>Created at</Text>
                    <div>{formatDateTime(jobData.created_at)}</div>
                  </div>
                </Col>
                <Col span={12}>
                  <Text type='secondary'>Modified by</Text>
                  <div>{jobData.position_info?.createdBy || 'Not specified'}</div>
                  <div style={{ marginTop: 8 }}>
                    <Text type='secondary'>Modified at</Text>
                    <div>{formatDateTime(jobData.updated_at)}</div>
                  </div>
                </Col>
              </Row>
            </Card>
          </Col>

          {/* Responsibilities, Skills, Qualifications */}
          <Col span={12}>
            <div style={{ paddingLeft: 12, borderLeft: '1px solid #f0f0f0' }}>
              <Title level={4}>Responsibilities</Title>
              <ul style={{ paddingLeft: 20 }}>
                {jobData.position_info?.mainResponsabilities
                  ?.split('. ')
                  .filter((r) => r.trim().length > 0)
                  .map((line, idx) => (
                    <li key={idx}>{line.trim().replace(/\.$/, '')}.</li>
                  ))}
              </ul>

              {/* Skills */}
              {jobData.position_info?.openPositionSkills?.length > 0 && (
                <>
                  <Divider />
                  <Title level={4}>Required Skills</Title>
                  <ul style={{ paddingLeft: 20 }}>
                    {jobData.position_info.openPositionSkills.map((skill, idx) => (
                      <li key={idx}>
                        {skill.skillName}
                        {skill.skillLeveName ? ` (${skill.skillLeveName})` : ''}
                      </li>
                    ))}
                  </ul>
                </>
              )}
              <Divider />
            </div>
          </Col>
        </Row>
      </div>
    </div>
  );
};
