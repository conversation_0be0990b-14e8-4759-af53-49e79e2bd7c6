import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { JobDescription } from './JobDescription';
import { MatchingsTable } from './MatchingsTable';
import { ManualMatching } from './ManualMatching';
import GenerateAIInterview from './GenerateAIInterview';
import Sidebar from '../layout/Sidebar';
import { Breadcrumbs } from '../layout/Breadcrumbs';
import { Card, Row, Col, Button } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import CandidateList from './CandidateList';
import { useNavigation } from '../../contexts/NavigationContext';

const JobDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { navigateBack } = useNavigation();
  const [selectedTab, setSelectedTab] = useState('job-description');
  const [selectedCandidates, setSelectedCandidates] = useState<string[]>([]);

  const handleTabChange = (key: string) => {
    setSelectedTab(key);
  };

  const handleCandidateSelect = (candidateId: string) => {
    setSelectedCandidates((prev) =>
      prev.includes(candidateId) ? prev.filter((id) => id !== candidateId) : [...prev, candidateId]
    );
  };

  return (
    <div style={{ background: '#f5f5f5', minHeight: 'calc(100vh - 80px)', padding: '24px' }}>
      {/* Breadcrumbs and Back Button aligned horizontally */}
      <Row
        align='middle'
        style={{
          marginBottom: '24px',
          padding: '0'
        }}
      >
        <Col>
          <Breadcrumbs />
        </Col>
      </Row>
      <div style={{ display: 'flex' }}>
        <Sidebar onSelect={handleTabChange} />
        <div style={{ flex: 1, padding: '0 0 0 24px' }}>
          {selectedTab === 'job-description' && <JobDescription />}
          {selectedTab === 'matching-candidates' && id && (
            <MatchingsTable id={id} onSelectCandidate={handleCandidateSelect} />
          )}
          {selectedTab === 'manual-matching' && id && (
            <ManualMatching id={id} />
          )}
          {selectedTab === 'generate-ai-interview' && id && (
            <Row gutter={24}>
              <Col xs={24} md={14}>
                <GenerateAIInterview id={id} />
              </Col>
              <Col xs={24} md={10}>
                <CandidateList positionId={id} />
              </Col>
            </Row>
          )}
        </div>
      </div>
    </div>
  );
};

export default JobDetails;
