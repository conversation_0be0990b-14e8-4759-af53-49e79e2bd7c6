/**
 * Performance monitoring utilities for E2E tests
 * Tracks test execution performance and identifies bottlenecks
 */

import { Page } from '@playwright/test';

export interface PerformanceMetrics {
  pageLoadTime: number;
  domContentLoadedTime: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  cumulativeLayoutShift: number;
  firstInputDelay: number;
  totalBlockingTime: number;
  networkRequests: number;
  slowRequests: Array<{
    url: string;
    duration: number;
    status: number;
  }>;
}

/**
 * Performance monitor for tracking page performance during tests
 */
export class PerformanceMonitor {
  private page: Page;
  private startTime: number = 0;
  private metrics: Partial<PerformanceMetrics> = {};

  constructor(page: Page) {
    this.page = page;
  }

  /**
   * Start monitoring performance
   */
  async startMonitoring() {
    this.startTime = Date.now();
    
    // Listen to network requests
    this.page.on('request', this.handleRequest.bind(this));
    this.page.on('response', this.handleResponse.bind(this));
    
    // Inject performance monitoring script
    await this.page.addInitScript(() => {
      // Store performance data on window object
      (window as any).performanceData = {
        navigationStart: performance.timeOrigin,
        requests: [],
        slowRequests: [],
      };
      
      // Monitor network requests
      const originalFetch = window.fetch;
      window.fetch = async function(...args) {
        const start = performance.now();
        const response = await originalFetch.apply(this, args);
        const duration = performance.now() - start;
        
        (window as any).performanceData.requests.push({
          url: args[0],
          duration,
          status: response.status,
        });
        
        if (duration > 1000) {
          (window as any).performanceData.slowRequests.push({
            url: args[0],
            duration,
            status: response.status,
          });
        }
        
        return response;
      };
    });
  }

  /**
   * Stop monitoring and collect metrics
   */
  async stopMonitoring(): Promise<PerformanceMetrics> {
    // Collect Web Vitals and performance metrics
    const webVitals = await this.collectWebVitals();
    const networkMetrics = await this.collectNetworkMetrics();
    
    this.metrics = {
      ...webVitals,
      ...networkMetrics,
      pageLoadTime: Date.now() - this.startTime,
    };
    
    return this.metrics as PerformanceMetrics;
  }

  /**
   * Collect Web Vitals metrics
   */
  private async collectWebVitals() {
    try {
      const vitals = await this.page.evaluate(() => {
        return new Promise((resolve) => {
          // Use PerformanceObserver to collect metrics
          const metrics: any = {};
          
          // Get navigation timing
          const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
          if (navigation) {
            metrics.domContentLoadedTime = navigation.domContentLoadedEventEnd - navigation.navigationStart;
            metrics.pageLoadTime = navigation.loadEventEnd - navigation.navigationStart;
          }
          
          // Try to get Web Vitals if available
          if ('web-vitals' in window) {
            // This would require web-vitals library to be loaded
            resolve(metrics);
          } else {
            // Fallback to basic performance metrics
            setTimeout(() => {
              const paintEntries = performance.getEntriesByType('paint');
              const fcpEntry = paintEntries.find(entry => entry.name === 'first-contentful-paint');
              
              if (fcpEntry) {
                metrics.firstContentfulPaint = fcpEntry.startTime;
              }
              
              resolve(metrics);
            }, 1000);
          }
        });
      });
      
      return vitals;
    } catch (error) {
      console.warn('Failed to collect Web Vitals:', error);
      return {};
    }
  }

  /**
   * Collect network performance metrics
   */
  private async collectNetworkMetrics() {
    try {
      const networkData = await this.page.evaluate(() => {
        const data = (window as any).performanceData || { requests: [], slowRequests: [] };
        return {
          networkRequests: data.requests.length,
          slowRequests: data.slowRequests,
        };
      });
      
      return networkData;
    } catch (error) {
      console.warn('Failed to collect network metrics:', error);
      return { networkRequests: 0, slowRequests: [] };
    }
  }

  /**
   * Handle request events
   */
  private handleRequest(request: any) {
    // Track request start time
    (request as any)._startTime = Date.now();
  }

  /**
   * Handle response events
   */
  private handleResponse(response: any) {
    const request = response.request();
    const startTime = (request as any)._startTime;
    
    if (startTime) {
      const duration = Date.now() - startTime;
      
      // Log slow requests
      if (duration > 2000) {
        console.warn(`Slow request detected: ${request.url()} (${duration}ms)`);
      }
    }
  }

  /**
   * Generate performance report
   */
  generateReport(): string {
    const metrics = this.metrics;
    
    let report = '\n=== Performance Report ===\n';
    
    if (metrics.pageLoadTime) {
      report += `Page Load Time: ${metrics.pageLoadTime}ms\n`;
    }
    
    if (metrics.domContentLoadedTime) {
      report += `DOM Content Loaded: ${metrics.domContentLoadedTime}ms\n`;
    }
    
    if (metrics.firstContentfulPaint) {
      report += `First Contentful Paint: ${metrics.firstContentfulPaint}ms\n`;
    }
    
    if (metrics.networkRequests) {
      report += `Network Requests: ${metrics.networkRequests}\n`;
    }
    
    if (metrics.slowRequests && metrics.slowRequests.length > 0) {
      report += `Slow Requests (>1s):\n`;
      metrics.slowRequests.forEach(req => {
        report += `  - ${req.url}: ${req.duration}ms (${req.status})\n`;
      });
    }
    
    report += '========================\n';
    
    return report;
  }

  /**
   * Check if performance meets thresholds
   */
  checkThresholds(thresholds: Partial<PerformanceMetrics> = {}): boolean {
    const defaultThresholds = {
      pageLoadTime: 5000,
      firstContentfulPaint: 2000,
      largestContentfulPaint: 4000,
      cumulativeLayoutShift: 0.1,
      firstInputDelay: 100,
    };
    
    const finalThresholds = { ...defaultThresholds, ...thresholds };
    const metrics = this.metrics;
    
    let passed = true;
    
    Object.entries(finalThresholds).forEach(([key, threshold]) => {
      const value = metrics[key as keyof PerformanceMetrics];
      if (typeof value === 'number' && value > threshold) {
        console.warn(`Performance threshold exceeded: ${key} = ${value} (threshold: ${threshold})`);
        passed = false;
      }
    });
    
    return passed;
  }
}

/**
 * Performance test utilities
 */
export class PerformanceTestUtils {
  /**
   * Measure page load performance
   */
  static async measurePageLoad(page: Page, url: string): Promise<PerformanceMetrics> {
    const monitor = new PerformanceMonitor(page);
    
    await monitor.startMonitoring();
    await page.goto(url);
    await page.waitForLoadState('networkidle');
    
    const metrics = await monitor.stopMonitoring();
    
    console.log(monitor.generateReport());
    
    return metrics;
  }

  /**
   * Measure action performance
   */
  static async measureAction(
    page: Page,
    action: () => Promise<void>,
    actionName: string = 'Action'
  ): Promise<number> {
    const startTime = Date.now();
    
    await action();
    
    const duration = Date.now() - startTime;
    console.log(`${actionName} completed in ${duration}ms`);
    
    return duration;
  }

  /**
   * Monitor memory usage during test
   */
  static async monitorMemory(page: Page): Promise<any> {
    try {
      const memoryInfo = await page.evaluate(() => {
        if ('memory' in performance) {
          return (performance as any).memory;
        }
        return null;
      });
      
      if (memoryInfo) {
        console.log('Memory usage:', {
          used: Math.round(memoryInfo.usedJSHeapSize / 1024 / 1024) + 'MB',
          total: Math.round(memoryInfo.totalJSHeapSize / 1024 / 1024) + 'MB',
          limit: Math.round(memoryInfo.jsHeapSizeLimit / 1024 / 1024) + 'MB',
        });
      }
      
      return memoryInfo;
    } catch (error) {
      console.warn('Memory monitoring not available:', error);
      return null;
    }
  }

  /**
   * Check for console errors and warnings
   */
  static setupConsoleMonitoring(page: Page) {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
        console.error('Console error:', msg.text());
      } else if (msg.type() === 'warning') {
        warnings.push(msg.text());
        console.warn('Console warning:', msg.text());
      }
    });
    
    return { errors, warnings };
  }
}
