import React, { useState, useEffect } from 'react';
import { Row, Table, Dropdown, Input, Modal, Button, Select, Tag, message, Menu } from 'antd';
import {
  MoreOutlined,
  CloudDownloadOutlined,
  StopOutlined,
  PlayCircleOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import './CandidatesTable.css';
import { useFetch } from '../../hooks/useFetch';
import { useDebounce } from '../../hooks/useDebounce';
import { UploadResume } from './UploadModal';
import type { Candidate } from '../../types/candidate';
import type { CandidateFilter, FilterOption } from '../../types/filters';
import { endpoints } from '../../utils/api';
import { api } from '../../utils/api';
import { useMsal } from '@azure/msal-react';
import { Link, useLocation } from 'react-router-dom';
import { Tooltip } from 'antd';
import { formatDateTime } from '../../utils/dateUtils';
import axios from 'axios';
import { useNavigation } from '../../contexts/NavigationContext';
import { TableFilterController } from '../common/TableFilterController';
import { FilterOptionsService } from '../../services/filterOptionsService';

const { Search } = Input;

interface CandidatesResponse {
  items: Candidate[];
  total_items: number;
}

export const CandidatesTable: React.FC = () => {
  const { instance } = useMsal();
  const activeAccount = instance.getActiveAccount();
  const location = useLocation();
  const { setPreviousPath, setCandidatesTableState } = useNavigation();

  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState<string | null>(null);
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useDebounce('', 300);

  // New filter state using CandidateFilter type
  const [filters, setFilters] = useState<CandidateFilter>({
    status: 'all',
    role: '',
    country: '',
    createdFrom: '',
    createdTo: '',
    searchTerm: ''
  });

  // Filter options state
  const [filterOptions, setFilterOptions] = useState<{
    roles?: FilterOption[];
    countries?: FilterOption[];
  }>({});
  const [filterOptionsLoading, setFilterOptionsLoading] = useState(false);

  const [disableModalVisible, setDisableModalVisible] = useState(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [candidateToDisable, setCandidateToDisable] = useState<string>('');
  const [disableReason, setDisableReason] = useState('');
  const [deleteReason, setDeleteReason] = useState('');
  const [disableConfirmLoading, setDisableConfirmLoading] = useState(false);
  const [deleteConfirmLoading, setDeleteConfirmLoading] = useState(false);

  const [enableModalVisible, setEnableModalVisible] = useState(false);
  const [enableReason, setEnableReason] = useState('');
  const [enableConfirmLoading, setEnableConfirmLoading] = useState(false);
  const [hrStatus, setHrStatus] = useState<string | undefined>(undefined);

  const closeDisableModal = () => {
    setDisableModalVisible(false);
    setDeleteModalVisible(false);
    setDisableReason('');
    setDeleteReason('');
    setCandidateToDisable('');
  };
  const closeDeleteModal = () => {
    setDisableModalVisible(false);
    setDeleteModalVisible(false);
    setDisableReason('');
    setDeleteReason('');
    setCandidateToDisable('');
  };

  const closeEnableModal = () => {
    setEnableModalVisible(false);
    setEnableReason('');
    setCandidateToDisable('');
  };

  // Helper function to convert status filter to API format
  const getStatusForAPI = (status: string) => {
    if (status === 'enabled') return true;
    if (status === 'disabled') return false;
    return undefined; // Don't send status param when 'all' is selected
  };

  const { data, loading, error, refetch } = useFetch<CandidatesResponse>({
    url: endpoints.candidates.list,
    method: 'POST',
    params: {
      chunk_size: limit,
      page
    },
    body: {
      ...(getStatusForAPI(filters.status || 'all') !== undefined && {
        status: getStatusForAPI(filters.status || 'all')
      }),
      search_term: debouncedSearchTerm ? searchTerm?.toLowerCase() : '',
      country: filters.country,
      role: filters.role,
      created_from: filters.createdFrom,
      created_to: filters.createdTo
    },
    dependencies: [
      limit,
      page,
      debouncedSearchTerm,
      filters.status,
      filters.country,
      filters.role,
      filters.createdFrom,
      filters.createdTo
    ]
  });

  const { data: projectData } = useFetch<{ id: string }[]>({
    url: endpoints.projects.list
  });

  useEffect(() => {
    setDebouncedSearchTerm(searchTerm || '');
    setFilters((prev) => ({ ...prev, searchTerm: searchTerm || '' }));
    setPage(1);
  }, [searchTerm]);

  // Load filter options
  useEffect(() => {
    const loadFilterOptions = async () => {
      setFilterOptionsLoading(true);
      try {
        const [roles, countries] = await Promise.all([
          FilterOptionsService.getRoles(),
          FilterOptionsService.getCountries()
        ]);
        setFilterOptions({ roles, countries });
      } catch (error) {
        console.error('Error loading filter options:', error);
      } finally {
        setFilterOptionsLoading(false);
      }
    };

    loadFilterOptions();
  }, []);

  // Restore state when component mounts if there's preserved state
  useEffect(() => {
    const preservedState = location.state?.preservedState;
    if (preservedState) {
      setSearchTerm(preservedState.searchTerm);
      setPage(preservedState.page);
      setLimit(preservedState.limit);
      setFilters({
        status: preservedState.status || 'all',
        country: preservedState.country || '',
        role: preservedState.role || '',
        createdFrom: preservedState.createdFrom || '',
        createdTo: preservedState.createdTo || '',
        searchTerm: preservedState.searchTerm || ''
      });
      // Clear the state from location to prevent repeated restoration
      window.history.replaceState({}, '');
    }
  }, [location.state]);

  // Filter handlers
  const handleFiltersChange = (newFilters: CandidateFilter) => {
    setFilters(newFilters);
    // Don't reset page here - wait for apply
  };

  const handleClearFilters = () => {
    setFilters({
      status: 'all',
      role: '',
      country: '',
      createdFrom: '',
      createdTo: '',
      searchTerm: ''
    });
    setPage(1);
  };

  const handleApplyFilters = () => {
    setPage(1); // Reset to first page when filters are applied
    // The API will be called automatically due to dependency changes
  };

  // Function to save current state before navigation
  const saveCurrentState = () => {
    setPreviousPath('/candidates');
    setCandidatesTableState({
      searchTerm,
      page,
      limit,
      status: filters.status || 'all',
      country: filters.country || '',
      role: filters.role || '',
      createdFrom: filters.createdFrom || '',
      createdTo: filters.createdTo || ''
    });
  };

  const handleDownload = async (identifier: string, documentType: 'pdf' | 'docx') => {
    try {
      // const response = await api.get(`/candidate/${identifier}/export`, {
      //   params: { document_type: documentType },
      //   responseType: 'blob'
      // });
      api
        .get(`/candidate/${identifier}/export`, {
          params: { document_type: documentType },
          responseType: 'blob'
        })
        .then((response) => {
          const disposition = response.headers['content-disposition'];
          const match = disposition?.match(/filename="(.+)"/);
          const filename =
            match?.[1] ||
            disposition?.split('filename=')[1]?.split(';')[0]?.trim() ||
            `candidate_${identifier}.${documentType}`;

          const url = window.URL.createObjectURL(new Blob([response.data]));
          const link = document.createElement('a');
          link.href = url;
          link.setAttribute('download', filename);
          document.body.appendChild(link);
          link.click();
          link.remove();
        });
    } catch (error) {
      console.error('Error downloading file:', error);
    }
  };

  const openDisableModal = (id: string) => {
    setCandidateToDisable(id);
    setDisableModalVisible(true);
  };

  const openEnableModal = (id: string) => {
    setCandidateToDisable(id);
    setEnableModalVisible(true);
  };

  const openDeleteModal = (id: string) => {
    setCandidateToDisable(id);
    setDeleteModalVisible(true);
  };

  const handleConfirmEnable = async () => {
    setEnableConfirmLoading(true);
    try {
      await api.post(endpoints.candidates.enable(candidateToDisable), {
        user: activeAccount?.username || 'unknown',
        timestamp: new Date().toISOString(),
        reason: enableReason
      });
      closeEnableModal();
      refetch();
    } catch (error) {
      console.error('Error enabling candidate:', error);
    } finally {
      setEnableConfirmLoading(false);
    }
  };

  const handleConfirmDisable = async () => {
    setDisableConfirmLoading(true);
    try {
      await api.post(endpoints.candidates.disable(candidateToDisable), {
        user: activeAccount?.username || 'unknown',
        timestamp: new Date().toISOString(),
        reason: disableReason
      });
      closeDisableModal();
      refetch();
    } catch (error) {
      console.error('Error disabling candidate:', error);
    } finally {
      setDisableConfirmLoading(false);
    }
  };

  const handleConfirmDelete = async () => {
    setDeleteConfirmLoading(true);
    try {
      await api.delete(endpoints.candidates.delete(candidateToDisable), {
        method: 'DELETE',
        data: {
          user: activeAccount?.username || 'unknown',
          timestamp: new Date().toISOString(),
          reason: deleteReason
        },
        headers: {
          'Content-Type': 'application/json'
        }
      });
      closeDeleteModal();
      refetch();
    } catch (error) {
      console.error('Error deleting candidate:', error);
    } finally {
      setDeleteConfirmLoading(false);
    }
  };

  const columns = [
    {
      title: 'Full Name',
      key: 'identifier',
      className: 'name-column',
      width: 300,
      ellipsis: true,
      render: (_: any, record: Candidate) => {
        return (
          <div className='table-cell-content'>
            <Link to={`/candidates/${record.id}`} className='table-text-truncate' onClick={saveCurrentState}>
              {record.candidate_info?.personal_info?.full_name ?? 'No name'}
            </Link>
          </div>
        );
      }
    },
    {
      title: 'Status',
      key: 'status',
      className: 'status-column',
      width: 120,
      render: (_: any, record: Candidate) => {
        const txt = record.is_active ? 'Enabled' : 'Disabled';
        const color = record.is_active ? 'green' : 'red';
        return (
          <div className='table-cell-content'>
            {record.is_active ? (
              <Tag color={color}>{txt}</Tag>
            ) : (
              <Tooltip title={record?.reason_info?.reason || 'No reason provided'}>
                <Tag color={color}>{txt}</Tag>
              </Tooltip>
            )}
          </div>
        );
      }
    },
    {
      title: 'Role',
      key: 'roles',
      className: 'role-column',
      width: 340,
      ellipsis: {
        showTitle: false
      },
      render: (_: any, record: Candidate) => {
        const jobTitle = record.candidate_info?.roles?.[0] || 'not_provided';
        if (!jobTitle || jobTitle.trim() === '') return 'not_provided';

        return (
          <div className='table-cell-content'>
            <Tooltip placement='topLeft' title={jobTitle}>
              <span className='table-text-truncate'>{jobTitle}</span>
            </Tooltip>
          </div>
        );
      }
    },
    {
      title: 'Email',
      key: 'email',
      className: 'email-column',
      width: 260,
      ellipsis: {
        showTitle: false
      },
      render: (_: any, record: Candidate) => {
        const email = record.candidate_info?.personal_info?.email ?? 'not_provided';
        return (
          <div className='table-cell-content'>
            <Tooltip placement='topLeft' title={email}>
              <span className='table-text-truncate'>{email}</span>
            </Tooltip>
          </div>
        );
      }
    },
    {
      title: 'Country',
      key: 'country',
      className: 'country-column',
      width: 210,
      ellipsis: true,
      render: (_: any, record: Candidate) => (
        <div className='table-cell-content'>
          <span className='table-text-truncate'>{record.candidate_info?.personal_info?.country ?? 'not_provided'}</span>
        </div>
      )
    },
    {
      title: 'Created Date',
      key: 'created_at',
      className: 'date-column',
      width: 170,
      render: (_: any, record: Candidate) => (
        <div className='table-cell-content'>
          <span className='table-text-truncate'>{record.created_at ? formatDateTime(record.created_at) : 'N/A'}</span>
        </div>
      )
    },
    {
      title: 'Created By',
      key: 'created_by',
      className: 'created-by-column',
      width: 170,
      ellipsis: {
        showTitle: false
      },
      render: (_: any, record: Candidate) => {
        const createdBy = record.created_by ?? 'not_provided';
        return (
          <div className='table-cell-content'>
            <Tooltip placement='topLeft' title={createdBy}>
              <span className='table-text-truncate'>{createdBy}</span>
            </Tooltip>
          </div>
        );
      }
    },
    {
      title: 'Updated By',
      key: 'updated_by',
      className: 'updated-by-column',
      width: 170,
      ellipsis: {
        showTitle: false
      },
      render: (_: any, record: Candidate) => {
        const updatedBy = record.updated_by ?? 'not_provided';
        return (
          <div className='table-cell-content'>
            <Tooltip placement='topLeft' title={updatedBy}>
              <span className='table-text-truncate'>{updatedBy}</span>
            </Tooltip>
          </div>
        );
      }
    },
    {
      title: 'Actions',
      key: 'actions',
      align: 'right' as const,
      fixed: 'right' as const,
      width: 80,
      render: (_: any, record: Candidate) => {
        const menuItems = [
          {
            key: 'pdf',
            label: (
              <span className='action-item'>
                <CloudDownloadOutlined className='action-icon' />
                Download PDF
              </span>
            ),
            onClick: () => handleDownload(record.id, 'pdf')
          },
          {
            key: 'docx',
            label: (
              <span className='action-item'>
                <CloudDownloadOutlined className='action-icon' />
                Download DOCX
              </span>
            ),
            onClick: () => handleDownload(record.id, 'docx')
          },
          {
            key: 'toggle',
            label: (
              <span className='action-item'>
                {record.is_active ? (
                  <StopOutlined className='action-icon' />
                ) : (
                  <PlayCircleOutlined className='action-icon' />
                )}
                {record.is_active ? 'Disable' : 'Enable'}
              </span>
            ),
            onClick: () => (record.is_active ? openDisableModal(record.id) : openEnableModal(record.id))
          },
          {
            key: 'delete',
            label: (
              <span className='action-item'>
                <DeleteOutlined className='action-icon' />
                Delete
              </span>
            ),
            onClick: () => openDeleteModal(record.id)
          }
        ];

        return (
          <Dropdown
            menu={{
              items: menuItems,
              className: 'candidates-actions-menu'
            }}
            placement='bottomRight'
            trigger={['click']}
          >
            <MoreOutlined style={{ cursor: 'pointer', padding: '4px', borderRadius: '4px' }} />
          </Dropdown>
        );
      }
    }
  ];

  if (error) return <p>Error: {error}</p>;

  return (
    <>
      <Row style={{ marginBottom: '24px' }}>
        <h1 style={{ margin: 0, fontSize: '28px', fontWeight: '600' }}>Candidates</h1>
      </Row>
      <Row
        className='candidates-search-row'
        style={{
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: '12px',
          gap: '16px',
          backgroundColor: '#fff',
          padding: '7px',
          borderRadius: '12px'
        }}
      >
        <div
          style={{
            fontSize: '16px',
            color: '#666',
            fontWeight: '500'
          }}
        >
          {data?.total_items || 0} Candidates Listed
        </div>
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '12px'
          }}
        >
          <Search
            style={{
              width: '400px',
              maxWidth: '400px'
            }}
            id='candidates-search-input'
            onChange={(e) => setSearchTerm(e.target.value)}
            onSearch={setSearchTerm}
            placeholder='Search Candidates'
            value={searchTerm || ''}
            enterButton
          />
          <TableFilterController
            filterType='candidate'
            filters={filters}
            onFiltersChange={handleFiltersChange}
            onClearFilters={handleClearFilters}
            onApplyFilters={handleApplyFilters}
            availableOptions={filterOptions}
            loading={filterOptionsLoading}
          />
          <UploadResume projectId={projectData?.[0]?.id || ''} refreshCandidatesTable={refetch} />
        </div>
      </Row>
      <Table
        className='candidates-table'
        loading={loading}
        columns={columns}
        dataSource={data?.items || []}
        scroll={{ x: 'max-content' }}
        pagination={{
          current: page,
          total: data?.total_items,
          pageSize: limit,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
          onChange: (newPage, newLimit) => {
            setPage(newPage);
            setLimit(newLimit);
          }
        }}
      />
      <Modal
        title='Enable Candidate'
        open={enableModalVisible}
        onCancel={closeEnableModal}
        footer={[
          <Button key='cancel' onClick={closeEnableModal}>
            Cancel
          </Button>,
          <Button
            key='confirm'
            type='primary'
            onClick={handleConfirmEnable}
            loading={enableConfirmLoading}
            disabled={enableConfirmLoading || !enableReason.trim()}
          >
            Confirm
          </Button>
        ]}
      >
        <Input.TextArea
          placeholder='Reason for enabling the candidate'
          autoSize={{ minRows: 3, maxRows: 6 }}
          value={enableReason}
          onChange={(e) => setEnableReason(e.target.value)}
        />
      </Modal>
      <Modal
        title='Disable Candidate'
        open={disableModalVisible}
        onCancel={closeDisableModal}
        footer={[
          <Button key='cancel' onClick={closeDisableModal}>
            Cancel
          </Button>,
          <Button
            key='confirm'
            type='primary'
            onClick={handleConfirmDisable}
            loading={disableConfirmLoading}
            disabled={disableConfirmLoading || !disableReason.trim()}
          >
            Confirm
          </Button>
        ]}
      >
        Choose a reason for desabling the candidate:
        <br />
        <Select
          placeholder='Reason for disabling the candidate'
          value={disableReason}
          onChange={setDisableReason}
          style={{ width: '300px' }}
        >
          <Select.Option value='Withdrawn from the process'>Withdrawn from the process</Select.Option>
          <Select.Option value='Rate too high'>Rate too high</Select.Option>
          <Select.Option value='Blacklisted'>Blacklisted</Select.Option>
        </Select>
      </Modal>

      <Modal
        title='Delete Candidate'
        open={deleteModalVisible}
        onCancel={closeDeleteModal}
        footer={[
          <Button key='cancel' onClick={closeDeleteModal}>
            Cancel
          </Button>,
          <Button
            key='confirm'
            type='primary'
            onClick={handleConfirmDelete}
            loading={deleteConfirmLoading}
            disabled={deleteConfirmLoading || !deleteReason.trim()}
          >
            Confirm
          </Button>
        ]}
      >
        <Input.TextArea
          placeholder='Reason for deleting the candidate'
          autoSize={{ minRows: 3, maxRows: 6 }}
          value={deleteReason}
          onChange={(e) => setDeleteReason(e.target.value)}
        />
      </Modal>
    </>
  );
};
