import React, { useState } from 'react';
import { Row, Col, Card, Spin, Button, Empty } from 'antd';
import { CandidateFeedbackDrawer } from './CandidateFeedbackDrawer';
import { useFetch } from '../../hooks/useFetch';
import { endpoints } from '../../utils/api';

interface CandidateFeedbackProps {
  candidateId: string;
  candidateInfo: any;
}

export const CandidateFeedback: React.FC<CandidateFeedbackProps> = ({ candidateId, candidateInfo }) => {
  const [selectedFeedback, setSelectedFeedback] = useState<any | null>(null);
  const [drawerOpen, setDrawerOpen] = useState(false);

  const { data, loading, error } = useFetch<any[]>({
    url: endpoints.interview.getByCandidateId(candidateId),
    dependencies: [candidateId]
  });

  return (
    <>
      {loading ? (
        <Spin style={{ margin: '2rem auto', display: 'block' }} />
      ) : !data || data.length === 0 ? (
        <Empty description="No feedback available" />
      ) : (
        <Row gutter={[16, 16]}>
          {data.map(item => (
            <Col span={24} key={item.position_id}>
              <Card
                title={item.position_info?.roleName || 'Position'}
                extra={<Button type="link" onClick={() => { setSelectedFeedback(item); setDrawerOpen(true); }}>View Details</Button>}
              >
                <div>Status: <b>{item.status_hr || 'N/A'}</b></div>
                <div>Recommendation: <b>{item.recommendation_tec ? 'Recommended' : 'Not Recommended'}</b></div>
                <div>Interview Date: <b>{item.interview_date_tec?.substr(0, 10) || 'N/A'}</b></div>
              </Card>
            </Col>
          ))}
        </Row>
      )}
      <CandidateFeedbackDrawer
        open={drawerOpen}
        onClose={() => setDrawerOpen(false)}
        feedback={selectedFeedback}
        candidateInfo={candidateInfo}
      />
    </>
  );
}; 