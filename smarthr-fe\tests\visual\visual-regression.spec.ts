import { test, expect } from '../fixtures';
import { createAssertions } from '../utils/assertions';

test.describe('Visual Regression Tests', () => {
  test('should match job orders page layout', async ({ jobOrdersPage }) => {
    await jobOrdersPage.goto();
    
    // Wait for page to fully load
    await jobOrdersPage.waitForPageLoad();
    
    // Take screenshot of the entire page
    await expect(jobOrdersPage.page).toHaveScreenshot('job-orders-page.png', {
      fullPage: true,
      animations: 'disabled'
    });
  });

  test('should match candidates page layout', async ({ candidatesPage }) => {
    await candidatesPage.goto();
    
    // Wait for page to fully load
    await candidatesPage.waitForPageLoad();
    
    // Take screenshot of the entire page
    await expect(candidatesPage.page).toHaveScreenshot('candidates-page.png', {
      fullPage: true,
      animations: 'disabled'
    });
  });

  test('should match job details page layout', async ({ loggedInPage, createdPositionId }) => {
    const assertions = createAssertions(loggedInPage);
    
    await loggedInPage.goto(`/job/${createdPositionId}`);
    await assertions.expectLoadingComplete();
    
    // Wait for all content to load
    await loggedInPage.waitForSelector('.ant-card, [data-testid="job-details"], .ant-tabs', { timeout: 10000 });
    
    // Take screenshot of the job details page
    await expect(loggedInPage).toHaveScreenshot('job-details-page.png', {
      fullPage: true,
      animations: 'disabled'
    });
  });

  test('should match navigation header', async ({ loggedInPage }) => {
    const assertions = createAssertions(loggedInPage);
    
    await loggedInPage.goto('/');
    await assertions.expectLoadingComplete();
    
    // Screenshot of the header/navigation area
    const header = loggedInPage.locator('.ant-layout-header, header, nav');
    if (await header.isVisible()) {
      await expect(header).toHaveScreenshot('navigation-header.png', {
        animations: 'disabled'
      });
    }
  });

  test('should match table components', async ({ jobOrdersPage }) => {
    await jobOrdersPage.goto();
    await jobOrdersPage.waitForPageLoad();
    
    // Screenshot of the jobs table
    const table = jobOrdersPage.jobTable;
    await expect(table).toHaveScreenshot('jobs-table.png', {
      animations: 'disabled'
    });
  });

  test('should match empty state layouts', async ({ candidatesPage }) => {
    await candidatesPage.goto();
    await candidatesPage.waitForPageLoad();
    
    // Search for something that doesn't exist to trigger empty state
    await candidatesPage.searchForCandidate('NonExistentCandidateXYZ123');
    
    // Wait for empty state to appear
    const emptyState = candidatesPage.page.locator('.ant-empty, .ant-table-placeholder');
    if (await emptyState.isVisible()) {
      await expect(emptyState).toHaveScreenshot('empty-state.png', {
        animations: 'disabled'
      });
    }
    
    // Clear search to restore normal state
    await candidatesPage.clearSearch();
  });

  test('should match modal dialogs', async ({ candidatesPage }) => {
    await candidatesPage.goto();
    await candidatesPage.waitForPageLoad();
    
    // Try to open add candidate modal
    const addButton = candidatesPage.addCandidateButton;
    if (await addButton.isVisible()) {
      await candidatesPage.clickAddCandidate();
      
      // Screenshot of the modal
      const modal = candidatesPage.page.locator('.ant-modal');
      if (await modal.isVisible()) {
        await expect(modal).toHaveScreenshot('add-candidate-modal.png', {
          animations: 'disabled'
        });
        
        // Close modal
        const closeButton = modal.locator('.ant-modal-close, button:has-text("Cancel")');
        if (await closeButton.isVisible()) {
          await closeButton.click();
        }
      }
    }
  });

  test('should match form components', async ({ loggedInPage }) => {
    const assertions = createAssertions(loggedInPage);
    
    await loggedInPage.goto('/');
    await assertions.expectLoadingComplete();
    
    // Look for search form
    const searchForm = loggedInPage.locator('.ant-input-search, .search-form');
    if (await searchForm.isVisible()) {
      await expect(searchForm).toHaveScreenshot('search-form.png', {
        animations: 'disabled'
      });
    }
  });

  test('should match filter components', async ({ jobOrdersPage }) => {
    await jobOrdersPage.goto();
    await jobOrdersPage.waitForPageLoad();
    
    // Screenshot of filter section
    const filterSection = jobOrdersPage.page.locator('.filters, .ant-form, .filter-section');
    if (await filterSection.isVisible()) {
      await expect(filterSection).toHaveScreenshot('filter-section.png', {
        animations: 'disabled'
      });
    }
  });

  test('should match pagination component', async ({ jobOrdersPage }) => {
    await jobOrdersPage.goto();
    await jobOrdersPage.waitForPageLoad();
    
    // Screenshot of pagination
    const pagination = jobOrdersPage.page.locator('.ant-pagination');
    if (await pagination.isVisible()) {
      await expect(pagination).toHaveScreenshot('pagination.png', {
        animations: 'disabled'
      });
    }
  });

  test('should match loading states', async ({ loggedInPage }) => {
    const assertions = createAssertions(loggedInPage);
    
    // Navigate to a page and capture loading state
    const navigationPromise = loggedInPage.goto('/candidates');
    
    // Try to capture loading spinner (this might be too fast)
    try {
      const loadingSpinner = loggedInPage.locator('.ant-spin-spinning');
      if (await loadingSpinner.isVisible({ timeout: 1000 })) {
        await expect(loadingSpinner).toHaveScreenshot('loading-spinner.png', {
          animations: 'disabled'
        });
      }
    } catch (error) {
      console.log('Loading state too fast to capture');
    }
    
    await navigationPromise;
    await assertions.expectLoadingComplete();
  });

  test('should match notification components', async ({ loggedInPage }) => {
    const assertions = createAssertions(loggedInPage);
    
    await loggedInPage.goto('/');
    await assertions.expectLoadingComplete();
    
    // Trigger a notification (this would need actual functionality)
    // For now, we'll inject a test notification
    await loggedInPage.evaluate(() => {
      // Simulate Ant Design notification
      const notification = document.createElement('div');
      notification.className = 'ant-notification ant-notification-topRight';
      notification.innerHTML = `
        <div class="ant-notification-notice ant-notification-notice-success">
          <div class="ant-notification-notice-content">
            <div class="ant-notification-notice-message">Success</div>
            <div class="ant-notification-notice-description">Test notification for visual regression</div>
          </div>
        </div>
      `;
      document.body.appendChild(notification);
    });
    
    // Screenshot of notification
    const notification = loggedInPage.locator('.ant-notification');
    if (await notification.isVisible()) {
      await expect(notification).toHaveScreenshot('notification.png', {
        animations: 'disabled'
      });
    }
    
    // Clean up
    await loggedInPage.evaluate(() => {
      const notification = document.querySelector('.ant-notification');
      if (notification) {
        notification.remove();
      }
    });
  });

  test('should match responsive layouts on mobile', async ({ loggedInPage }) => {
    const assertions = createAssertions(loggedInPage);
    
    // Set mobile viewport
    await loggedInPage.setViewportSize({ width: 375, height: 667 });
    
    await loggedInPage.goto('/');
    await assertions.expectLoadingComplete();
    
    // Take mobile screenshot
    await expect(loggedInPage).toHaveScreenshot('mobile-home-page.png', {
      fullPage: true,
      animations: 'disabled'
    });
    
    // Test candidates page on mobile
    await loggedInPage.goto('/candidates');
    await assertions.expectLoadingComplete();
    
    await expect(loggedInPage).toHaveScreenshot('mobile-candidates-page.png', {
      fullPage: true,
      animations: 'disabled'
    });
    
    // Reset to desktop viewport
    await loggedInPage.setViewportSize({ width: 1280, height: 720 });
  });

  test('should match responsive layouts on tablet', async ({ loggedInPage }) => {
    const assertions = createAssertions(loggedInPage);
    
    // Set tablet viewport
    await loggedInPage.setViewportSize({ width: 768, height: 1024 });
    
    await loggedInPage.goto('/');
    await assertions.expectLoadingComplete();
    
    // Take tablet screenshot
    await expect(loggedInPage).toHaveScreenshot('tablet-home-page.png', {
      fullPage: true,
      animations: 'disabled'
    });
    
    // Reset to desktop viewport
    await loggedInPage.setViewportSize({ width: 1280, height: 720 });
  });

  test('should match dark theme layouts', async ({ loggedInPage }) => {
    const assertions = createAssertions(loggedInPage);
    
    await loggedInPage.goto('/');
    await assertions.expectLoadingComplete();
    
    // Try to enable dark theme (if available)
    const themeToggle = loggedInPage.locator('.theme-toggle, button:has-text("Dark"), .dark-mode-toggle');
    if (await themeToggle.isVisible()) {
      await themeToggle.click();
      await assertions.expectLoadingComplete();
      
      // Take dark theme screenshot
      await expect(loggedInPage).toHaveScreenshot('dark-theme-home-page.png', {
        fullPage: true,
        animations: 'disabled'
      });
      
      // Switch back to light theme
      await themeToggle.click();
    } else {
      console.log('Dark theme toggle not available');
    }
  });

  test('should match error state layouts', async ({ loggedInPage }) => {
    const assertions = createAssertions(loggedInPage);
    
    // Navigate to non-existent page to trigger 404
    await loggedInPage.goto('/non-existent-page');
    await assertions.expectLoadingComplete();
    
    // Screenshot of error page
    const errorPage = loggedInPage.locator('.ant-result-404, .not-found, .error-page');
    if (await errorPage.isVisible()) {
      await expect(errorPage).toHaveScreenshot('error-404-page.png', {
        animations: 'disabled'
      });
    } else {
      // If redirected to home, take screenshot of that
      await expect(loggedInPage).toHaveScreenshot('error-redirect-page.png', {
        fullPage: true,
        animations: 'disabled'
      });
    }
  });
});

test.describe('Visual Regression - Component Focus', () => {
  test('should match button states', async ({ loggedInPage }) => {
    const assertions = createAssertions(loggedInPage);
    
    await loggedInPage.goto('/');
    await assertions.expectLoadingComplete();
    
    // Find various button types
    const buttons = loggedInPage.locator('.ant-btn');
    const buttonCount = await buttons.count();
    
    if (buttonCount > 0) {
      // Screenshot first few buttons
      for (let i = 0; i < Math.min(buttonCount, 3); i++) {
        const button = buttons.nth(i);
        if (await button.isVisible()) {
          await expect(button).toHaveScreenshot(`button-${i}.png`, {
            animations: 'disabled'
          });
        }
      }
    }
  });

  test('should match input field states', async ({ loggedInPage }) => {
    const assertions = createAssertions(loggedInPage);
    
    await loggedInPage.goto('/');
    await assertions.expectLoadingComplete();
    
    // Find input fields
    const inputs = loggedInPage.locator('.ant-input, input');
    const inputCount = await inputs.count();
    
    if (inputCount > 0) {
      const firstInput = inputs.first();
      
      // Normal state
      await expect(firstInput).toHaveScreenshot('input-normal.png', {
        animations: 'disabled'
      });
      
      // Focused state
      await firstInput.focus();
      await expect(firstInput).toHaveScreenshot('input-focused.png', {
        animations: 'disabled'
      });
      
      // With text
      await firstInput.fill('Test input text');
      await expect(firstInput).toHaveScreenshot('input-with-text.png', {
        animations: 'disabled'
      });
    }
  });
});
