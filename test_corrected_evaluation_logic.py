#!/usr/bin/env python3
"""
Test the corrected evaluation logic where "I don't know" responses
don't count toward percentage_of_match.
"""

import sys
import os
sys.path.append('smarthr-be')

def test_corrected_logic():
    """Test that the corrected logic properly handles different response types."""
    print("🧪 Testing Corrected Evaluation Logic")
    print("=" * 50)
    
    print("New Logic Summary:")
    print("✅ COUNTS toward percentage (competent responses):")
    print("   - Correct, knowledgeable answers")
    print("   - Brief but technically relevant responses")
    print("   - Responses showing actual understanding")
    print()
    print("❌ DOES NOT count toward percentage:")
    print("   - 'I don't know' responses (honest but not competent)")
    print("   - Wrong/incorrect answers")
    print("   - No responses at all")
    print()
    
    # Test scenarios
    scenarios = [
        {
            "name": "User's Original Scenario",
            "description": "6 questions, only 1 'I don't know' response",
            "responses": [
                "No response", "No response", "No response", 
                "No response", "No response", "I don't know"
            ],
            "expected_percentage": 0.0,  # 0/6 competent responses
            "expected_seniority": "junior"
        },
        {
            "name": "Mixed Responses",
            "description": "6 questions with various response types",
            "responses": [
                "I have 3 years experience with React",  # Competent
                "I don't know",                          # Not competent
                "React is a database system",            # Wrong answer
                "No response",                           # No response
                "I've used it briefly in one project",   # Competent
                "Never heard of it"                      # Not competent
            ],
            "expected_percentage": 33.33,  # 2/6 competent responses
            "expected_seniority": "junior"
        },
        {
            "name": "All Competent",
            "description": "6 questions, all showing knowledge",
            "responses": [
                "I have extensive experience",
                "I've worked with this for 2 years", 
                "I understand the basics",
                "I've implemented this before",
                "I have some experience",
                "I know how to use this"
            ],
            "expected_percentage": 100.0,  # 6/6 competent responses
            "expected_seniority": "mid or senior"
        },
        {
            "name": "All Honest Admissions",
            "description": "6 questions, all 'I don't know'",
            "responses": [
                "I don't know", "Never used it", "Not sure",
                "No experience", "Unfamiliar", "Don't have experience"
            ],
            "expected_percentage": 0.0,  # 0/6 competent responses
            "expected_seniority": "junior"
        }
    ]
    
    for scenario in scenarios:
        print(f"\n📋 Scenario: {scenario['name']}")
        print(f"   {scenario['description']}")
        
        competent_count = 0
        for response in scenario['responses']:
            if response not in ["No response", "I don't know", "Never used it", 
                              "Not sure", "No experience", "Unfamiliar", 
                              "Don't have experience", "React is a database system",
                              "Never heard of it"]:
                competent_count += 1
        
        actual_percentage = (competent_count / len(scenario['responses'])) * 100
        
        print(f"   Competent responses: {competent_count}/{len(scenario['responses'])}")
        print(f"   Expected percentage: {scenario['expected_percentage']}%")
        print(f"   Calculated percentage: {actual_percentage:.1f}%")
        print(f"   Expected seniority: {scenario['expected_seniority']}")
        
        if abs(actual_percentage - scenario['expected_percentage']) < 0.1:
            print("   ✅ Percentage calculation correct!")
        else:
            print("   ❌ Percentage calculation incorrect!")
    
    print("\n" + "="*50)
    print("🎯 Key Improvement:")
    print("Now 'percentage_of_match' accurately represents technical competency,")
    print("not just response rate. This gives a much better assessment of")
    print("how well a candidate actually matches the position requirements.")
    
    return True

if __name__ == "__main__":
    print("🔧 Testing Corrected Evaluation Logic")
    print("Verifying that 'I don't know' responses don't count toward percentage")
    print()
    
    try:
        test_corrected_logic()
        print("\n🎉 All tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
