# SmartHR Docker Setup

This document explains how to use Docker for development and testing with the SmartHR application.

## Prerequisites

- Docker Desktop installed and running
- Docker Compose v2.0 or higher

## Project Structure

```
smarthr/
├── docker-compose.yml          # Main development setup
├── docker-compose.test.yml     # Testing configuration
├── .env.example               # Environment variables template
├── smarthr-be/               # Backend (Python FastAPI)
│   └── Dockerfile
├── smarthr-fe/               # Frontend (React + Vite)
│   └── Dockerfile
└── README-Docker.md          # This file
```

## Quick Start

### 1. Environment Setup

Copy the environment template and configure your values:

```bash
cp .env.example .env
# Edit .env with your actual configuration values
```

### 2. Development Mode

Start both frontend and backend services:

```bash
# Build and start all services
docker-compose up --build

# Or run in detached mode
docker-compose up -d --build
```

Services will be available at:
- **Frontend**: http://localhost:5173
- **Backend**: http://localhost:8080

### 3. Testing

Run tests for both services:

```bash
# Run frontend tests
docker-compose -f docker-compose.test.yml run --rm smarthr-frontend-test

# Run backend tests (requires proper environment configuration)
docker-compose -f docker-compose.test.yml run --rm smarthr-backend-test

# Run all tests
docker-compose -f docker-compose.test.yml up --abort-on-container-exit
```

## Available Commands

### Development Commands

```bash
# Start services
docker-compose up

# Rebuild and start
docker-compose up --build

# Stop services
docker-compose down

# View logs
docker-compose logs
docker-compose logs smarthr-frontend
docker-compose logs smarthr-backend

# Execute commands in running containers
docker-compose exec smarthr-frontend npm run lint
docker-compose exec smarthr-backend python -m pytest
```

### Testing Commands

```bash
# Frontend tests only
docker-compose -f docker-compose.test.yml run --rm smarthr-frontend-test

# Backend tests only
docker-compose -f docker-compose.test.yml run --rm smarthr-backend-test

# Run tests with coverage
docker-compose -f docker-compose.test.yml run --rm smarthr-frontend-test npm run test:coverage
```

## Docker Images

### Frontend (smarthr-fe)
- **Base**: Node.js 18 Alpine
- **Development**: Hot-reload enabled, runs on port 5173
- **Production**: Nginx serving static files on port 80
- **Test**: Runs vitest with jsdom environment

### Backend (smarthr-be)
- **Base**: Python 3.11 slim
- **Production**: FastAPI with uvicorn on port 8080
- **Test**: Runs pytest with test environment variables

## Environment Variables

Key environment variables (see `.env.example` for complete list):

### Backend
- `AZURE_OPENAI_*`: Azure OpenAI configuration
- `POSTGRES_*`: Database connection settings
- `LANGCHAIN_*`: LangChain/LangSmith settings

### Frontend
- `VITE_API_URL`: Backend API URL (default: http://localhost:8080)
- `NODE_ENV`: Environment mode

## Troubleshooting

### Port Conflicts
If you get "port already allocated" errors:
```bash
# Check what's using the port
netstat -ano | findstr :8080
netstat -ano | findstr :5173

# Stop conflicting services or change ports in docker-compose.yml
```

### Volume Issues
If you have permission issues with volumes:
```bash
# Reset Docker volumes
docker-compose down -v
docker-compose up --build
```

### Backend Test Issues
Backend tests may fail if external services (LangChain, Azure) aren't properly configured. Ensure test environment variables are set correctly in `docker-compose.test.yml`.

## Development Workflow

1. **Start Development Environment**:
   ```bash
   docker-compose up --build
   ```

2. **Make Code Changes**: Files are mounted as volumes, so changes are reflected immediately

3. **Run Tests**:
   ```bash
   docker-compose -f docker-compose.test.yml run --rm smarthr-frontend-test
   ```

4. **Debug Issues**: Use logs and exec commands to troubleshoot

5. **Clean Up**:
   ```bash
   docker-compose down
   docker system prune  # Optional: clean up unused images
   ```

## Production Deployment

For production deployment, use the production targets:

```bash
# Build production images
docker-compose -f docker-compose.prod.yml build

# Deploy (configuration depends on your deployment platform)
docker-compose -f docker-compose.prod.yml up -d
```

Note: Create `docker-compose.prod.yml` for production-specific configuration.
