import { createRoot } from 'react-dom/client';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { PublicClientApplication, EventType, AuthenticationResult } from '@azure/msal-browser';
import React from 'react';
import { App } from './App';
import { msalConfig } from './auth/auth.config';
import './index.css';

const msalInstance = new PublicClientApplication(msalConfig);

// Set active account
if (!msalInstance.getActiveAccount() && msalInstance.getAllAccounts().length > 0) {
  msalInstance.setActiveAccount(msalInstance.getAllAccounts()[0]);
}

// Listen for sign-in event and set active account
msalInstance.addEventCallback((event) => {
  if (event.eventType === EventType.LOGIN_SUCCESS) {
    const result = event.payload as AuthenticationResult;
    if (result.account) {
      msalInstance.setActiveAccount(result.account);
    }
  }
});

const root = createRoot(document.getElementById('root')!);
root.render(
  <BrowserRouter>
    <App instance={msalInstance} />
  </BrowserRouter>
); 