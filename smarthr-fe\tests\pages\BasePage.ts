import { Page, Locator, expect } from '@playwright/test';
import { waitForAppLoad, waitForApiCalls } from '../utils/test-helpers';

/**
 * Base page class with common functionality for all pages
 */
export abstract class BasePage {
  protected page: Page;

  constructor(page: Page) {
    this.page = page;
  }

  /**
   * Navigate to the page and wait for it to load
   */
  async goto(url: string = this.getUrl()) {
    await this.page.goto(url);
    await waitForAppLoad(this.page);
    await waitForApiCalls(this.page);
    await this.waitForPageLoad();
  }

  /**
   * Get the URL for this page
   */
  abstract getUrl(): string;

  /**
   * Wait for page-specific elements to load
   */
  abstract waitForPageLoad(): Promise<void>;

  /**
   * Common header elements
   */
  get header() {
    return this.page.locator('header, .ant-layout-header');
  }

  get navbar() {
    return this.page.locator('.ant-menu');
  }

  get loadingSpinner() {
    return this.page.locator('.ant-spin-spinning');
  }

  /**
   * Navigation methods
   */
  async navigateToJobs() {
    await this.page.click('a[href="/"], .ant-menu-item:has-text("Open Positions")');
    await waitForAppLoad(this.page);
  }

  async navigateToCandidates() {
    await this.page.click('a[href="/candidates"], .ant-menu-item:has-text("Candidates")');
    await waitForAppLoad(this.page);
  }

  /**
   * Wait for loading to complete
   */
  async waitForLoading() {
    await this.loadingSpinner.waitFor({ state: 'hidden', timeout: 30000 });
  }

  /**
   * Take a screenshot of the current page
   */
  async takeScreenshot(name: string) {
    await this.page.screenshot({ 
      path: `test-results/screenshots/${name}-${Date.now()}.png`,
      fullPage: true 
    });
  }

  /**
   * Wait for a notification to appear
   */
  async waitForNotification(expectedText?: string) {
    const notification = this.page.locator('.ant-message, .ant-notification');
    await notification.waitFor({ state: 'visible' });
    
    if (expectedText) {
      await expect(notification).toContainText(expectedText);
    }
    
    // Wait for it to disappear
    await notification.waitFor({ state: 'hidden', timeout: 10000 });
  }

  /**
   * Fill an Ant Design form field
   */
  async fillAntdInput(selector: string, value: string) {
    await this.page.waitForSelector(selector);
    await this.page.fill(selector, value);
    await this.page.waitForTimeout(100); // Wait for onChange handlers
  }

  /**
   * Click an Ant Design button and wait for action
   */
  async clickAntdButton(text: string, waitForSelector?: string) {
    const button = this.page.locator(`.ant-btn:has-text("${text}")`);
    await button.waitFor({ state: 'visible' });
    await button.click();
    
    if (waitForSelector) {
      await this.page.waitForSelector(waitForSelector);
    } else {
      await waitForApiCalls(this.page);
    }
  }

  /**
   * Select option from Ant Design Select
   */
  async selectAntdOption(selectSelector: string, optionText: string) {
    await this.page.click(selectSelector);
    await this.page.waitForSelector('.ant-select-dropdown');
    await this.page.click(`.ant-select-item-option[title="${optionText}"]`);
    await this.page.waitForSelector('.ant-select-dropdown', { state: 'hidden' });
  }

  /**
   * Check if element exists and is visible
   */
  async isElementVisible(selector: string): Promise<boolean> {
    try {
      await this.page.waitForSelector(selector, { state: 'visible', timeout: 5000 });
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get text content of an element
   */
  async getElementText(selector: string): Promise<string> {
    await this.page.waitForSelector(selector);
    return await this.page.textContent(selector) || '';
  }

  /**
   * Wait for table to load data
   */
  async waitForTableData(tableSelector = '.ant-table-tbody') {
    await this.page.waitForSelector(tableSelector);
    await this.page.waitForFunction(
      (selector) => {
        const tbody = document.querySelector(selector);
        return tbody && tbody.children.length > 0;
      },
      tableSelector,
      { timeout: 10000 }
    );
  }
}
