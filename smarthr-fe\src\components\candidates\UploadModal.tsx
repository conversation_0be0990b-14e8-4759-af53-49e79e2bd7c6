import React, { useState } from 'react';
import { Upload, message, Modal, Button, List, Avatar, Spin, Checkbox, Alert, Tag } from 'antd';
import { FolderOpenOutlined, PlusOutlined } from '@ant-design/icons';
import { max_upload_files, api, max_files_size } from '../../utils/api';
import type { CandidateInfo } from '../../types/candidate';
import { updateCandidate } from '../../services/candidateService';
import { useMsal } from '@azure/msal-react';

const { Dragger } = Upload;

interface UploadResumeProps {
  projectId: string;
  refreshCandidatesTable: () => void;
}

interface ExistingCandidate {
  candidate_id: string;
  candidate_info: {
    personal_info: {
      full_name: string;
      email: string;
    };
  };
  error?: boolean;
  error_message?: string;
}

export const UploadResume: React.FC<UploadResumeProps> = ({ projectId, refreshCandidatesTable }) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [fileList, setFileList] = useState<any[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadSummary, setUploadSummary] = useState<{ total: number; duplicated: number } | null>(null);
  const [duplicatedCandidates, setDuplicatedCandidates] = useState<ExistingCandidate[]>([]);
  const [selectedCandidateIds, setSelectedCandidateIds] = useState<string[]>([]);
  const [updateErrors, setUpdateErrors] = useState<Record<string, string>>({});
  const [candidatePayloads, setCandidatePayloads] = useState<Record<string, CandidateInfo>>({});
  const [showLimitMessage, setShowLimitMessage] = useState(true);
  const { instance } = useMsal();
  const activeAccount = instance.getActiveAccount();

  message.config({
    maxCount: 1
  });

  const resetModal = () => {
    setIsModalVisible(false);
    setFileList([]);
    setIsUploading(false);
    setUploadSummary(null);
    setDuplicatedCandidates([]);
    setSelectedCandidateIds([]);
    setUpdateErrors({});
    setCandidatePayloads({});
  };

  const handleUpload = async () => {
    if (!fileList.length) return;
    setIsUploading(true);
    setUploadSummary(null);
    setDuplicatedCandidates([]);
    setSelectedCandidateIds([]);
    setUpdateErrors({});
    setCandidatePayloads({});
    try {
      const formData = new FormData();
      fileList.forEach((file) => {
        formData.append('files', file.originFileObj || file);
      });
      const response = await api.post(
        `/candidate/uploadfiles/?project_id=${projectId}&created_by=${activeAccount?.username || 'unknown'}`,
        formData,
        {
          headers: { 'Content-Type': 'multipart/form-data' }
        }
      );
      const data = response.data;
      setUploadSummary({ total: fileList.length, duplicated: data.length });
      setDuplicatedCandidates(data);
      setSelectedCandidateIds([]);
      const payloadMap: Record<string, CandidateInfo> = {};
      data.forEach((candidate: any) => {
        payloadMap[candidate.id] = candidate.candidate_info;
      });
      setCandidatePayloads(payloadMap);
      if (data.length === 0) {
        message.success('All candidates uploaded successfully.');
        resetModal();
        refreshCandidatesTable();
      }
      refreshCandidatesTable();
    } catch (error: any) {
      message.error('Upload failed. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleUpdateSelected = async () => {
    setIsUploading(true);
    const errors: Record<string, string> = {};
    for (const id of selectedCandidateIds) {
      const candidate = duplicatedCandidates.find((c) => c.candidate_id === id);
      if (!candidate) continue;
      try {
        const updatePayload = {
          id: candidate.candidate_id,
          proj_id: projectId,
          candidate_info: candidate.candidate_info,
          suggested_positions: [{}],
          analysis_status: 'string',
          updated_by: activeAccount?.username || 'unknown'
        };
        await updateCandidate(updatePayload);
      } catch (err: any) {
        errors[id] = err?.message || 'Update failed';
      }
    }
    setUpdateErrors(errors);
    setIsUploading(false);
    if (Object.keys(errors).length === 0) {
      message.success('Selected candidates updated successfully.');
      resetModal();
      refreshCandidatesTable();
    }
  };

  const handleSingleUpdate = async (id: string) => {
    setIsUploading(true);
    const errors: Record<string, string> = {};
    const candidate = duplicatedCandidates.find((c) => c.candidate_id === id);
    if (!candidate) return;
    try {
      const updatePayload = {
        id: candidate.candidate_id,
        proj_id: projectId,
        candidate_info: candidate.candidate_info,
        suggested_positions: [{}],
        analysis_status: 'string'
      };
      await updateCandidate(updatePayload);
    } catch (err: any) {
      errors[id] = err?.message || 'Update failed';
    }
    setUpdateErrors(errors);
    setIsUploading(false);
    if (Object.keys(errors).length === 0) {
      message.success('Selected candidates updated successfully.');
      resetModal();
      refreshCandidatesTable();
    }
  };

  const uploadProps = {
    name: 'file',
    multiple: true,
    fileList,
    beforeUpload: (file: any) => {
      if (file.size > max_files_size) {
        message.error('File size must be less than 2MB!');
        return Upload.LIST_IGNORE;
      }
      if (fileList.length >= max_upload_files) {
        if (showLimitMessage) {
          message.error(
            'You can only upload up to ' +
              max_upload_files.toString() +
              ' files. Only the first ' +
              max_upload_files.toString() +
              ' files are kept.'
          );
          setShowLimitMessage(!showLimitMessage);
          setTimeout(() => setShowLimitMessage(showLimitMessage), 2000);
          return Upload.LIST_IGNORE;
        }
      }
      if (
        file.type !== 'application/pdf' &&
        file.type !== 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ) {
        message.error('You can only upload PDF or DOCX files!');
        return Upload.LIST_IGNORE;
      }
      setFileList((prev) => [...prev, file]);
      return false; // prevent auto upload
    },
    onRemove: (file: any) => {
      setFileList((prev) => prev.filter((f) => f.uid !== file.uid));
    },
    disabled: isUploading,
    accept: '.pdf,.docx',
    format: 'application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    maxCount: max_upload_files
  };

  const handleChange = ({ fileList: newFileList }) => {
    if (newFileList.length > max_upload_files || fileList.length > max_upload_files) {
      // If the new file list exceeds the max_upload_files limit, slice it to the max_upload_files limit
      newFileList = newFileList.slice(0, max_upload_files);
      //setHasShownLimitMessage(true);
      if (showLimitMessage) {
        message.error(
          'You can only upload up to ' +
            max_upload_files.toString() +
            ' files. Only the first ' +
            max_upload_files.toString() +
            ' files are kept.'
        );
        setShowLimitMessage(!showLimitMessage);
        setTimeout(() => setShowLimitMessage(showLimitMessage), 2000);
      }
    }
    setFileList(newFileList);
  };

  return (
    <>
      <Button 
        type='primary' 
        onClick={() => setIsModalVisible(true)}
        icon={<PlusOutlined />}
        style={{ 
          backgroundColor: '#7B66FF', 
          borderColor: '#7B66FF',
          display: 'flex',
          alignItems: 'center',
          gap: '4px'
        }}
      >
        New Candidate
      </Button>
      <Modal
        title={duplicatedCandidates.length > 0 ? 'Upload results' : 'Add New Candidates'}
        open={isModalVisible}
        destroyOnClose
        onCancel={resetModal}
        closable={!isUploading}
        maskClosable={!isUploading}
        footer={null}
      >
        {duplicatedCandidates.length > 0 ? (
          <>
            {duplicatedCandidates.filter((candidate) => candidate.error_message === 'created').length > 0 && (
              <Alert
                message={
                  'We created ' +
                  duplicatedCandidates.filter((candidate) => candidate.error_message === 'created').length +
                  ' new candidate(s).'
                }
                type='success'
                showIcon
                style={{ marginBottom: 16 }}
              />
            )}
            {duplicatedCandidates.filter((candidate) => candidate.error_message === 'duplicated').length > 0 && (
              <Alert
                message={
                  'We found ' +
                  duplicatedCandidates.filter((candidate) => candidate.error_message === 'duplicated').length +
                  " duplicate resume(s). Choose which ones you'd like to update."
                }
                type='warning'
                showIcon
                style={{ marginBottom: 16 }}
              />
            )}
            <List
              itemLayout='horizontal'
              dataSource={duplicatedCandidates}
              renderItem={(candidate) => {
                //if (!candidate.candidate_info?.personal_info) return null;
                const id = candidate.candidate_id;
                return (
                  <List.Item
                    key={id}
                    style={{
                      backgroundColor: selectedCandidateIds.includes(id) ? '#e6f7ff' : 'transparent',
                      marginBottom: 8
                    }}
                  >
                    {candidate?.error_message === 'duplicated' && (
                      <Checkbox
                        checked={selectedCandidateIds.includes(id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedCandidateIds((prev) => [...prev, id]);
                          } else {
                            setSelectedCandidateIds((prev) => prev.filter((candidateId) => candidateId !== id));
                          }
                        }}
                        disabled={candidate?.error_message !== 'duplicated'}
                        style={{ marginRight: 16 }}
                      />
                    )}
                    <List.Item.Meta
                      title={
                        candidate.candidate_info?.personal_info?.full_name || <span style={{ color: '' }}>No name</span>
                      }
                      description={
                        candidate.error &&
                        candidate?.error_message != 'duplicated' && (
                          <>
                            <span style={{ color: 'red' }}>{candidate?.error_message}</span>
                          </>
                        )
                      }
                    />
                    {candidate.error && candidate.error_message === 'duplicated' && (
                      <>
                        <Tag color='orange' style={{ marginRight: 16, clear: 'both' }}>
                          Duplicated
                        </Tag>
                      </>
                    )}
                    {!candidate.error && candidate.error_message == 'created' && (
                      <>
                        <Tag color='green' style={{ marginRight: 16, clear: 'both' }}>
                          Created
                        </Tag>
                      </>
                    )}
                  </List.Item>
                );
              }}
            />
            <div style={{ marginTop: 16, textAlign: 'right' }}>
              <Button onClick={resetModal} disabled={isUploading} style={{ marginRight: 8 }}>
                Cancel
              </Button>
              <Button
                type='primary'
                onClick={() => handleUpdateSelected()}
                disabled={isUploading || selectedCandidateIds.length === 0}
                style={{ marginRight: 8 }}
                loading={isUploading}
              >
                Update
              </Button>
            </div>
          </>
        ) : (
          <>
            <Dragger {...uploadProps} onChange={handleChange}>
              <p className='ant-upload-drag-icon'>
                <FolderOpenOutlined />
              </p>
              <p className='ant-upload-text'>Click or drag file to this area to upload</p>
              <p className='ant-upload-hint'>
                You can upload up to 5 files at once<br />
                (PDF or DOCX, max 2MB each).<br />
                Single or bulk uploads supported.
              </p>
              {isUploading && (
                <div style={{ textAlign: 'center', marginTop: 16 }}>
                  <Spin />
                </div>
              )}
            </Dragger>
            <div style={{ marginTop: 16, textAlign: 'right' }}>
              <Button onClick={resetModal} disabled={isUploading} style={{ marginRight: 8 }}>
                Cancel
              </Button>
              <Button
                type='primary'
                onClick={handleUpload}
                disabled={fileList.length === 0 || isUploading}
                loading={isUploading}
                style={{ 
                  backgroundColor: '#7B66FF', 
                  borderColor: '#7B66FF',
                  color: '#FFFFFF'
                }}
              >
                Upload
              </Button>
            </div>
          </>
        )}
      </Modal>
    </>
  );
};

export default UploadResume;
