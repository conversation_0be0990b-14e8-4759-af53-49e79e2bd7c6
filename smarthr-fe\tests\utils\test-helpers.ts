import { Page, expect } from '@playwright/test';

/**
 * Common test utilities and helpers for SmartHR E2E tests
 */

/**
 * Wait for the application to be fully loaded
 */
export async function waitForAppLoad(page: Page) {
  // Wait for the main app container
  await page.waitForSelector('.App', { timeout: 30000 });
  
  // Wait for any loading spinners to disappear
  await page.waitForFunction(() => {
    const spinners = document.querySelectorAll('.ant-spin-spinning');
    return spinners.length === 0;
  }, { timeout: 10000 });
}

/**
 * Wait for API calls to complete
 */
export async function waitForApiCalls(page: Page, timeout = 10000) {
  await page.waitForLoadState('networkidle', { timeout });
}

/**
 * Take a screenshot with a descriptive name
 */
export async function takeScreenshot(page: Page, name: string) {
  await page.screenshot({ 
    path: `test-results/screenshots/${name}-${Date.now()}.png`,
    fullPage: true 
  });
}

/**
 * Fill form field with proper waiting
 */
export async function fillFormField(page: Page, selector: string, value: string) {
  await page.waitForSelector(selector);
  await page.fill(selector, value);
  // Wait a bit for any validation or onChange handlers
  await page.waitForTimeout(100);
}

/**
 * Click button and wait for action to complete
 */
export async function clickAndWait(page: Page, selector: string, waitForSelector?: string) {
  await page.waitForSelector(selector);
  await page.click(selector);
  
  if (waitForSelector) {
    await page.waitForSelector(waitForSelector);
  } else {
    // Default wait for network idle
    await waitForApiCalls(page);
  }
}

/**
 * Select option from Ant Design Select component
 */
export async function selectAntdOption(page: Page, selectSelector: string, optionText: string) {
  // Click the select to open dropdown
  await page.click(selectSelector);
  
  // Wait for dropdown to appear
  await page.waitForSelector('.ant-select-dropdown');
  
  // Click the option
  await page.click(`.ant-select-item-option[title="${optionText}"]`);
  
  // Wait for dropdown to close
  await page.waitForSelector('.ant-select-dropdown', { state: 'hidden' });
}

/**
 * Handle Ant Design date picker
 */
export async function selectAntdDate(page: Page, datePickerSelector: string, date: string) {
  await page.click(datePickerSelector);
  await page.waitForSelector('.ant-picker-dropdown');
  
  // For simplicity, just type the date
  await page.fill(`${datePickerSelector} input`, date);
  await page.press(`${datePickerSelector} input`, 'Enter');
  
  // Wait for picker to close
  await page.waitForSelector('.ant-picker-dropdown', { state: 'hidden' });
}

/**
 * Wait for notification/message to appear and disappear
 */
export async function waitForNotification(page: Page, expectedText?: string) {
  const notification = page.locator('.ant-message, .ant-notification');
  await notification.waitFor({ state: 'visible' });
  
  if (expectedText) {
    await expect(notification).toContainText(expectedText);
  }
  
  // Wait for it to disappear
  await notification.waitFor({ state: 'hidden', timeout: 10000 });
}

/**
 * Navigate to a route and wait for it to load
 */
export async function navigateToRoute(page: Page, route: string) {
  await page.goto(route);
  await waitForAppLoad(page);
  await waitForApiCalls(page);
}

/**
 * Check if element is visible and enabled
 */
export async function isElementReady(page: Page, selector: string): Promise<boolean> {
  try {
    const element = page.locator(selector);
    await element.waitFor({ state: 'visible', timeout: 5000 });
    return await element.isEnabled();
  } catch {
    return false;
  }
}

/**
 * Retry an action with exponential backoff
 */
export async function retryAction<T>(
  action: () => Promise<T>,
  maxRetries = 3,
  baseDelay = 1000
): Promise<T> {
  let lastError: Error;

  for (let i = 0; i < maxRetries; i++) {
    try {
      return await action();
    } catch (error) {
      lastError = error as Error;
      if (i < maxRetries - 1) {
        const delay = baseDelay * Math.pow(2, i);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  throw lastError!;
}

/**
 * Error handling utilities
 */
export class ErrorHandler {
  /**
   * Handle and log test errors
   */
  static async handleTestError(error: Error, context: string, page?: Page) {
    console.error(`Test error in ${context}:`, error.message);

    if (page) {
      // Take screenshot on error
      await page.screenshot({
        path: `test-results/error-screenshots/${context}-${Date.now()}.png`,
        fullPage: true
      });

      // Log page URL and title
      console.error(`Page URL: ${page.url()}`);
      console.error(`Page title: ${await page.title()}`);

      // Log console errors
      const logs = await page.evaluate(() => {
        return (window as any).testLogs || [];
      });
      if (logs.length > 0) {
        console.error('Browser console logs:', logs);
      }
    }

    throw error;
  }

  /**
   * Wrap test action with error handling
   */
  static async withErrorHandling<T>(
    action: () => Promise<T>,
    context: string,
    page?: Page
  ): Promise<T> {
    try {
      return await action();
    } catch (error) {
      await this.handleTestError(error as Error, context, page);
      throw error; // This line won't be reached, but TypeScript needs it
    }
  }
}

/**
 * Performance monitoring utilities
 */
export class PerformanceMonitor {
  private page: Page;
  private startTime: number = 0;

  constructor(page: Page) {
    this.page = page;
  }

  /**
   * Start performance monitoring
   */
  start() {
    this.startTime = Date.now();
  }

  /**
   * End performance monitoring and return duration
   */
  end(): number {
    return Date.now() - this.startTime;
  }

  /**
   * Measure page load time
   */
  async measurePageLoad(url: string): Promise<number> {
    const start = Date.now();
    await this.page.goto(url);
    await waitForAppLoad(this.page);
    return Date.now() - start;
  }

  /**
   * Measure API response time
   */
  async measureApiResponse(urlPattern: string | RegExp): Promise<number> {
    const start = Date.now();
    await this.page.waitForResponse(urlPattern);
    return Date.now() - start;
  }
}
