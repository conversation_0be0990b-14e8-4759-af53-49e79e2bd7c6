import os
import re
import subprocess
from datetime import datetime
from models.candidate import Candidate


# Generate a unique filename for the candidate based on their name, date, and role.
def get_filename(candidate: Candidate, file_extension: str) -> str:
    """
    Generate a unique filename for the candidate based on their name, date, and role.

    Args:
        candidate (Candidate): The candidate object.
        file_extension (str): The file extension.

    Returns:
        str: The generated filename.
    """
    filename = "[BGSF-Arroyo]_"
    cand_name = candidate.candidate_info.get("personal_info").get("full_name") if candidate.candidate_info.get("personal_info").get("full_name") else "Unknown_Candidate"
    now = datetime.now()
    now_str = now.strftime("%m%d%Y")
    roles = candidate.candidate_info.get("work_experience")

    if roles and isinstance(roles[0], dict):
        roles = roles[0].get("job_title") if roles[0].get("job_title") else roles[1].get("job_title") if len(roles) > 1 and roles[1] else ""

    if roles and isinstance(roles, str):
        now_str = f"{now_str}_{roles}"
    return sanitize_filename(f"{filename}{cand_name}_{now_str}.{file_extension}")


# Sanitize the filename to remove invalid characters and replace whitespace with underscores.
def sanitize_filename(name: str) -> str:
    """
    Sanitize filename to remove invalid characters and replace whitespace with underscores.

    Args:
        name (str): The filename to sanitize.

    Returns:
        str: The sanitized filename.
    """
    sanitized = re.sub(r'[\/\\:\*\?"<>\|]', '', name)
    sanitized = re.sub(r'\s+', '_', sanitized.strip())
    return sanitized


# Convert a DOCX file to PDF using LibreOffice.
def convert_docx_to_pdf(input_path, output_dir):
    """
    Convert a DOCX file to PDF using LibreOffice.

    Args:
        input_path (str): Path to the input DOCX file.
        output_dir (str): Directory to save the converted PDF.

    Returns:
        str: Path to the converted PDF file.
    """
    try:
        subprocess.run([
            "libreoffice",
            "--headless",
            "--convert-to", "pdf",
            "--outdir", output_dir,
            input_path
        ], check=True)
        return os.path.join(output_dir, os.path.splitext(os.path.basename(input_path))[0] + ".pdf")
    except subprocess.CalledProcessError as e:
        # You may want to log this error in your main logger
        return None
