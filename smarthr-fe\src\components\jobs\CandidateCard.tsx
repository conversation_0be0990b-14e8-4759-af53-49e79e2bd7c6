import React from 'react';
import { Card, Avatar } from 'antd';
import { Candidate } from '../../types/candidate';
import ProgressCircle from '../common/ProgressCircle';
import { getResumePercentage, getTechInterviewPercentage, getCandidateInitials } from '../../utils/candidateUtils';
import { CANDIDATE_CARD_STYLES, COLORS, SIZES } from '../../utils/candidateStyles';

interface CandidateCardProps {
  candidate: Candidate;
  onClick: (candidate: Candidate) => void;
}

export const CandidateCard: React.FC<CandidateCardProps> = ({ candidate, onClick }) => {
  const resumePercentage = getResumePercentage(candidate);
  const techInterviewPercentage = getTechInterviewPercentage(candidate);
  const initials = getCandidateInitials(candidate.candidate_info.full_name);

  return (
    <Card
      key={candidate.id}
      onClick={() => onClick(candidate)}
      style={CANDIDATE_CARD_STYLES.container}
      styles={{ body: { padding: 16 } }}
    >
      <div style={CANDIDATE_CARD_STYLES.header}>
        <Avatar style={CANDIDATE_CARD_STYLES.avatar}>{initials}</Avatar>
        <span style={CANDIDATE_CARD_STYLES.name}>{candidate.candidate_info.full_name}</span>
      </div>

      <div style={CANDIDATE_CARD_STYLES.separator} />

      <div style={CANDIDATE_CARD_STYLES.progressContainer}>
        <div style={CANDIDATE_CARD_STYLES.progressSection}>
          <ProgressCircle
            percent={resumePercentage}
            size={SIZES.PROGRESS_LARGE}
            strokeColor={COLORS.PROGRESS_GREEN}
            strokeWidth={6}
            fontSize={12}
            fontWeight='bold'
            textColor={COLORS.TEXT_PRIMARY}
          />
          <span style={CANDIDATE_CARD_STYLES.progressLabel}>RESUME</span>
        </div>

        <div style={CANDIDATE_CARD_STYLES.progressSection}>
          <ProgressCircle
            percent={techInterviewPercentage}
            size={SIZES.PROGRESS_LARGE}
            strokeColor={COLORS.PROGRESS_GREEN}
            strokeWidth={6}
            fontSize={12}
            fontWeight='bold'
            textColor={COLORS.TEXT_PRIMARY}
          />
          <span style={CANDIDATE_CARD_STYLES.progressLabel}>TECH INTERVIEW</span>
        </div>
      </div>
    </Card>
  );
};

export default CandidateCard;
