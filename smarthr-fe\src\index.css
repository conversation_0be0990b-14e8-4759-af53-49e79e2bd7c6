:root {
  --border-radius: 4px;
  --primary-color: #7B66FF;
  --background-color: #f5f5f5;
  --text-color: #333333;
  --secondary-text-color: #8c8c8c;
  --white: #ffffff;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #f5222d;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif,
    'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  padding: 0;
  background-color: var(--background-color);
  color: var(--text-color);
  line-height: 1.5;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
}

/* header {
  display: flex;
  flex-direction: row;
  align-content: center;
  justify-content: space-between;
  background: #dcf1ff;
} */

.ant-table-thead > tr > th {
  background-color: var(--white) !important;
  font-weight: 600;
}

.ant-tag {
  border-radius: 2px;
}

.ant-card {
  box-shadow: none;
}

.ant-breadcrumb a {
  color: var(--primary-color);
}
