import { test, expect } from '../fixtures';
import { createAssertions } from '../utils/assertions';

test.describe('Authentication', () => {
  test('should load the application without authentication errors', async ({ page }) => {
    const assertions = createAssertions(page);
    
    await page.goto('/');
    
    // Wait for the app to load
    await page.waitForSelector('.App, [data-testid="app-container"]', { timeout: 30000 });
    
    // Check that we don't have any authentication errors
    const errorElements = page.locator('.ant-message-error, .error-message');
    await expect(errorElements).toHaveCount(0);
    
    // Verify the page loaded successfully
    await assertions.expectLoadingComplete();
  });

  test('should handle authentication state correctly', async ({ page, authHelper }) => {
    const assertions = createAssertions(page);
    
    await page.goto('/');
    
    // Check if authentication is required
    const isLoggedIn = await authHelper.isLoggedIn();
    
    if (isLoggedIn) {
      // If logged in, should see main navigation
      await expect(page.locator('.ant-menu, nav')).toBeVisible();
      
      // Should be able to access protected routes
      await page.goto('/candidates');
      await assertions.expectLoadingComplete();
      await expect(page.locator('.ant-table, [data-testid="candidates-table"]')).toBeVisible();
    } else {
      // If not logged in, should either see login form or be redirected
      const hasLoginButton = await page.locator('button:has-text("Login"), .ant-btn:has-text("Sign in")').isVisible();
      const hasMainContent = await page.locator('.ant-menu, nav').isVisible();
      
      // Should have either login UI or main content (for development mode)
      expect(hasLoginButton || hasMainContent).toBeTruthy();
    }
  });

  test('should maintain authentication state across page reloads', async ({ loggedInPage }) => {
    const assertions = createAssertions(loggedInPage);
    
    // Navigate to a protected page
    await loggedInPage.goto('/candidates');
    await assertions.expectLoadingComplete();
    
    // Verify we can access the page
    await expect(loggedInPage.locator('.ant-table, [data-testid="candidates-table"]')).toBeVisible();
    
    // Reload the page
    await loggedInPage.reload();
    await assertions.expectLoadingComplete();
    
    // Should still be able to access the page
    await expect(loggedInPage.locator('.ant-table, [data-testid="candidates-table"]')).toBeVisible();
  });

  test('should handle logout correctly', async ({ loggedInPage, authHelper }) => {
    const assertions = createAssertions(loggedInPage);
    
    // Verify we're logged in
    const isLoggedIn = await authHelper.isLoggedIn();
    expect(isLoggedIn).toBeTruthy();
    
    // Attempt logout
    await authHelper.logout();
    
    // Wait for logout to complete
    await assertions.expectLoadingComplete();
    
    // Verify logout state
    const isStillLoggedIn = await authHelper.isLoggedIn();
    
    if (!isStillLoggedIn) {
      // If logout worked, should not be able to access protected content
      // or should see login UI
      const hasLoginButton = await loggedInPage.locator('button:has-text("Login"), .ant-btn:has-text("Sign in")').isVisible();
      const hasProtectedContent = await loggedInPage.locator('.ant-menu, nav').isVisible();
      
      // Should either have login UI or no protected content
      if (hasProtectedContent) {
        console.log('App appears to be in development mode without authentication');
      } else {
        expect(hasLoginButton).toBeTruthy();
      }
    } else {
      console.log('Logout not implemented or app is in development mode');
    }
  });

  test('should handle different user roles', async ({ page, context }) => {
    const assertions = createAssertions(page);
    
    // Test with admin user
    const { loginAsUser } = await import('../utils/auth');
    await loginAsUser(page, context, 'admin');
    
    await page.goto('/');
    await assertions.expectLoadingComplete();
    
    // Admin should have access to all features
    await page.goto('/candidates');
    await assertions.expectLoadingComplete();
    await expect(page.locator('.ant-table, [data-testid="candidates-table"]')).toBeVisible();
    
    // Test with recruiter user (if different permissions exist)
    await loginAsUser(page, context, 'recruiter');
    
    await page.goto('/');
    await assertions.expectLoadingComplete();
    
    // Recruiter should also have access to main features
    await page.goto('/candidates');
    await assertions.expectLoadingComplete();
    await expect(page.locator('.ant-table, [data-testid="candidates-table"]')).toBeVisible();
  });

  test('should handle authentication errors gracefully', async ({ page }) => {
    const assertions = createAssertions(page);
    
    // Navigate to the app
    await page.goto('/');
    
    // Wait for initial load
    await page.waitForSelector('.App, [data-testid="app-container"]', { timeout: 30000 });
    
    // Check for any authentication error messages
    const authErrors = page.locator('.ant-message-error:has-text("auth"), .error:has-text("login")');
    
    // Should not have authentication errors on initial load
    await expect(authErrors).toHaveCount(0);
    
    // Verify the app is functional
    await assertions.expectLoadingComplete();
    
    // Should be able to navigate (either to login or main content)
    const hasNavigation = await page.locator('.ant-menu, nav, button:has-text("Login")').isVisible();
    expect(hasNavigation).toBeTruthy();
  });

  test('should preserve user session data', async ({ loggedInPage, authHelper }) => {
    // Get current user info
    const userInfo = await authHelper.getCurrentUser();
    
    if (userInfo) {
      // Navigate to different pages
      await loggedInPage.goto('/');
      await loggedInPage.goto('/candidates');
      
      // User info should still be available
      const currentUserInfo = await authHelper.getCurrentUser();
      expect(currentUserInfo).toBeTruthy();
      
      // Basic user properties should match
      if (userInfo.username && currentUserInfo.username) {
        expect(currentUserInfo.username).toBe(userInfo.username);
      }
    } else {
      console.log('No user session data available - app may be in development mode');
    }
  });
});

test.describe('Authentication Edge Cases', () => {
  test('should handle network errors during authentication', async ({ page }) => {
    // Simulate network issues
    await page.route('**/auth/**', route => route.abort());
    await page.route('**/login/**', route => route.abort());
    
    await page.goto('/');
    
    // App should still load, even if auth endpoints fail
    await page.waitForSelector('.App, [data-testid="app-container"]', { timeout: 30000 });
    
    // Should handle the error gracefully
    const hasErrorHandling = await page.locator('.ant-message, .error-boundary, .App').isVisible();
    expect(hasErrorHandling).toBeTruthy();
  });

  test('should handle expired tokens gracefully', async ({ loggedInPage }) => {
    // Simulate expired token by clearing auth data
    await loggedInPage.evaluate(() => {
      // Clear any auth tokens that might be expired
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.includes('token') || key.includes('auth')) {
          localStorage.removeItem(key);
        }
      });
    });
    
    // Try to access a protected route
    await loggedInPage.goto('/candidates');
    
    // Should either redirect to login or handle gracefully
    await loggedInPage.waitForLoadState('networkidle');
    
    // Should not crash the application
    const hasContent = await loggedInPage.locator('.App, .ant-table, button:has-text("Login")').isVisible();
    expect(hasContent).toBeTruthy();
  });
});
