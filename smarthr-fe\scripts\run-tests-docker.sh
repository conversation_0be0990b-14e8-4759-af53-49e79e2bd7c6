#!/bin/bash

# Script to run Playwright E2E tests in Docker environment
# Usage: ./scripts/run-tests-docker.sh [options]

set -e

# Default values
CLEANUP=true
HEADLESS=true
WORKERS=2
PROJECT=""
GREP=""
TIMEOUT=60000

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help              Show this help message"
    echo "  --no-cleanup            Don't clean up containers after tests"
    echo "  --headed                Run tests in headed mode (with browser UI)"
    echo "  --workers N             Number of parallel workers (default: 2)"
    echo "  --project PROJECT       Run specific project (chromium, firefox, webkit, etc.)"
    echo "  --grep PATTERN          Run tests matching pattern"
    echo "  --timeout MS            Test timeout in milliseconds (default: 60000)"
    echo "  --debug                 Run in debug mode"
    echo "  --update-snapshots      Update visual regression snapshots"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Run all tests"
    echo "  $0 --project chromium                # Run only Chromium tests"
    echo "  $0 --grep 'authentication'           # Run authentication tests only"
    echo "  $0 --headed --workers 1              # Run in headed mode with 1 worker"
    echo "  $0 --update-snapshots                # Update visual regression snapshots"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_usage
            exit 0
            ;;
        --no-cleanup)
            CLEANUP=false
            shift
            ;;
        --headed)
            HEADLESS=false
            shift
            ;;
        --workers)
            WORKERS="$2"
            shift 2
            ;;
        --project)
            PROJECT="$2"
            shift 2
            ;;
        --grep)
            GREP="$2"
            shift 2
            ;;
        --timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        --debug)
            DEBUG=true
            shift
            ;;
        --update-snapshots)
            UPDATE_SNAPSHOTS=true
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Function to cleanup containers
cleanup() {
    if [ "$CLEANUP" = true ]; then
        print_status "Cleaning up containers..."
        docker-compose -f docker-compose.test.yml down -v --remove-orphans
        print_success "Cleanup completed"
    else
        print_warning "Skipping cleanup (containers left running)"
    fi
}

# Function to wait for services
wait_for_services() {
    print_status "Waiting for services to be ready..."
    
    # Wait for database
    print_status "Waiting for PostgreSQL..."
    timeout 60 bash -c 'until docker-compose -f docker-compose.test.yml exec -T postgres pg_isready -h localhost -p 5432; do sleep 2; done'
    
    # Wait for backend
    print_status "Waiting for backend API..."
    timeout 60 bash -c 'until curl -f http://localhost:8080/health 2>/dev/null; do sleep 2; done'
    
    # Wait for frontend
    print_status "Waiting for frontend..."
    timeout 60 bash -c 'until curl -f http://localhost:5173 2>/dev/null; do sleep 2; done'
    
    print_success "All services are ready"
}

# Function to build test command
build_test_command() {
    local cmd="npx playwright test"
    
    if [ "$HEADLESS" = false ]; then
        cmd="$cmd --headed"
    fi
    
    if [ -n "$WORKERS" ]; then
        cmd="$cmd --workers=$WORKERS"
    fi
    
    if [ -n "$PROJECT" ]; then
        cmd="$cmd --project=$PROJECT"
    fi
    
    if [ -n "$GREP" ]; then
        cmd="$cmd --grep=\"$GREP\""
    fi
    
    if [ -n "$TIMEOUT" ]; then
        cmd="$cmd --timeout=$TIMEOUT"
    fi
    
    if [ "$DEBUG" = true ]; then
        cmd="$cmd --debug"
    fi
    
    if [ "$UPDATE_SNAPSHOTS" = true ]; then
        cmd="$cmd --update-snapshots"
    fi
    
    echo "$cmd"
}

# Trap to ensure cleanup on exit
trap cleanup EXIT

# Main execution
print_status "Starting SmartHR E2E Tests in Docker"

# Check if Docker and Docker Compose are available
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed or not in PATH"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed or not in PATH"
    exit 1
fi

# Create necessary directories
mkdir -p test-results playwright-report

# Start services
print_status "Starting services..."
docker-compose -f docker-compose.test.yml up -d --build

# Wait for services to be ready
wait_for_services

# Build test command
TEST_CMD=$(build_test_command)
print_status "Running tests with command: $TEST_CMD"

# Run tests
if docker-compose -f docker-compose.test.yml exec -T playwright-tests bash -c "$TEST_CMD"; then
    print_success "Tests completed successfully!"
    
    # Copy test results from container
    print_status "Copying test results..."
    docker-compose -f docker-compose.test.yml cp playwright-tests:/app/test-results ./test-results/
    docker-compose -f docker-compose.test.yml cp playwright-tests:/app/playwright-report ./playwright-report/
    
    print_success "Test results copied to local directories"
    print_status "HTML Report: ./playwright-report/index.html"
    print_status "JSON Results: ./test-results/results.json"
    
    exit 0
else
    print_error "Tests failed!"
    
    # Copy test results even on failure
    print_status "Copying test results..."
    docker-compose -f docker-compose.test.yml cp playwright-tests:/app/test-results ./test-results/ || true
    docker-compose -f docker-compose.test.yml cp playwright-tests:/app/playwright-report ./playwright-report/ || true
    
    # Show container logs for debugging
    print_status "Container logs:"
    docker-compose -f docker-compose.test.yml logs playwright-tests
    
    exit 1
fi
