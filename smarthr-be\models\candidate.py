from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, field_validator


class CandidateBase(BaseModel):
    proj_id: str
    candidate_info: dict
    suggested_positions: Optional[List[dict]] = None
    analysis_status: Optional[str] = None
    last_matching: Optional[datetime]
    is_active: bool
    reason_info: Optional[dict]


class CandidateCreate(BaseModel):
    proj_id: str
    candidate_info: dict
    suggested_positions: Optional[List[dict]] = None
    analysis_status: Optional[str] = None
    created_by: str


class CandidateUpdate(BaseModel):
    id: str
    proj_id: str
    candidate_info: dict
    suggested_positions: Optional[List[dict]] = None
    analysis_status: Optional[str] = None
    updated_by: str


class Candidate(CandidateBase):
    id: str
    to_be_embebbed: str = None
    embedding: List[float] = None
    sparse_embedding: dict = None
    created_at: datetime
    created_by: Optional[str] = None
    updated_at: datetime
    updated_by: Optional[str] = None

    class Config:
        from_attributes = True
        orm_mode = True
        validate_by_name = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


class UploadFileResponse(BaseModel):
    candidate_id: Optional[str] = None
    candidate_info: dict
    error: bool = False
    error_message: Optional[str] = None


class CandidateFilters(BaseModel):
    status: Optional[bool] = None  # True for active, False for inactive
    search_term: Optional[str] = None  # Search term for candidate info
    country: Optional[str] = None
    role: Optional[str] = None  # e.g. "Frontend Developer"
    created_by: Optional[str] = None  # e.g. "John Doe"
    created_from: Optional[datetime] = None
    created_to: Optional[datetime] = None

    @field_validator('created_from', 'created_to', mode='before')
    @classmethod
    def parse_empty_string_as_none(cls, v):
        if v == '':
            return None
        return v
