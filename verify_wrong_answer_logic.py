#!/usr/bin/env python3
"""
Verification script for the enhanced interview evaluation logic.
Tests that wrong answers don't count toward percentage calculation.
"""

import sys
import os

# Add the smarthr-be directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'smarthr-be'))

def test_enhanced_prompts():
    """Test that enhanced prompts are in place"""
    try:
        with open('smarthr-be/controllers/interview_controller.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("✅ Enhanced prompts verification:")
        print(f"  - RESPONSE CLASSIFICATION RULES found: {content.count('RESPONSE CLASSIFICATION RULES')} times")
        print(f"  - 'COUNTS TOWARD PERCENTAGE' found: {content.count('COUNTS TOWARD PERCENTAGE')} times")
        print(f"  - 'DOES NOT COUNT TOWARD PERCENTAGE' found: {content.count('DOES NOT COUNT TOWARD PERCENTAGE')} times")
        print(f"  - 'wrong answer' examples found: {content.count('wrong answer')} times")
        print(f"  - Validation function found: {'def _validate_evaluation_result' in content}")
        
        # Check for key phrases that indicate the new logic
        key_phrases = [
            "Only count valid responses toward percentage",
            "DO NOT count wrong/incorrect answers toward percentage",
            "exclude wrong/incorrect answers"
        ]
        
        for phrase in key_phrases:
            if phrase in content:
                print(f"  ✅ Found key phrase: '{phrase}'")
            else:
                print(f"  ❌ Missing key phrase: '{phrase}'")
        
        return True
        
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return False

def test_validation_logic():
    """Test the validation logic handles wrong answers correctly"""
    try:
        # Import the validation function
        from controllers.interview_controller import _validate_evaluation_result
        from models.interview import EvaluationResult, QuestionEvaluation, Seniority
        
        print("\n✅ Testing validation logic:")
        
        # Test case 1: Mix of valid and invalid responses
        result1 = EvaluationResult(
            overall_seniority=Seniority.JUNIOR,
            per_question=[
                QuestionEvaluation(
                    question_number=1,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.JUNIOR,
                    explanation="Candidate said 'I don't know React' - honest admission"
                ),
                QuestionEvaluation(
                    question_number=2,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.JUNIOR,
                    explanation="Candidate said 'React is a database' - completely wrong answer"
                )
            ],
            percentage_of_match=50.0,  # Only 1 valid response out of 2
            explanation="Mixed responses"
        )
        
        issues1 = _validate_evaluation_result(result1, 2, "test")
        print(f"  - Test 1 (50% with wrong answer): {len(issues1)} validation issues")
        
        # Test case 2: All wrong answers
        result2 = EvaluationResult(
            overall_seniority=Seniority.JUNIOR,
            per_question=[
                QuestionEvaluation(
                    question_number=1,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.JUNIOR,
                    explanation="Candidate said 'React is a database' - wrong answer"
                ),
                QuestionEvaluation(
                    question_number=2,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.JUNIOR,
                    explanation="Candidate said 'Microservices are just big files' - incorrect response"
                )
            ],
            percentage_of_match=0.0,  # No valid responses
            explanation="All wrong answers"
        )
        
        issues2 = _validate_evaluation_result(result2, 2, "test")
        print(f"  - Test 2 (0% with all wrong answers): {len(issues2)} validation issues")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing validation logic: {e}")
        return False

def main():
    """Main verification function"""
    print("🔍 Verifying enhanced interview evaluation logic...")
    print("=" * 60)
    
    success = True
    
    # Test 1: Enhanced prompts
    if not test_enhanced_prompts():
        success = False
    
    # Test 2: Validation logic
    if not test_validation_logic():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("✅ ALL VERIFICATIONS PASSED!")
        print("🎯 The system now correctly handles wrong answers:")
        print("   - 'I don't know' responses COUNT toward percentage (valid)")
        print("   - Wrong/incorrect answers DO NOT count toward percentage (invalid)")
        print("   - Validation logic detects misclassifications")
    else:
        print("❌ SOME VERIFICATIONS FAILED!")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
