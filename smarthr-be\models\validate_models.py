from typing import List

from pydantic import BaseModel, Field


class ValidateCandidateRequest(BaseModel):
    project_id: str = Field(..., description="ID of the project")
    candidate_info: dict = Field(..., description="Candidate information")
    keys_to_eval: List[str] = Field(
        ..., description="List of keys to evaluate for validation"
    )

class ValidateCandidateItem(BaseModel):
    id: str = Field(..., description="ID of the candidate")
    average_distance: float
    candidate_info: dict = Field(..., description="Candidate information")
    

class ValidateCandidateResponse(BaseModel):
    matching_candidates: List[ValidateCandidateItem] = Field(..., description="List of matching candidate IDs")