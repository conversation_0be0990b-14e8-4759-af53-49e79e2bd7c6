# Node modules
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Test results and reports
test-results/
playwright-report/
coverage/

# Build outputs
dist/
build/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Docker files (don't copy into container)
Dockerfile*
docker-compose*.yml
.dockerignore

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Temporary files
tmp/
temp/
