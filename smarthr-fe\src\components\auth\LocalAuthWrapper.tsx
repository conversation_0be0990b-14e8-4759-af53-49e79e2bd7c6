import React from 'react';
import { AuthenticatedTemplate, UnauthenticatedTemplate, useMsal } from '@azure/msal-react';
import { Button } from 'antd';
import { loginRequest } from '../../auth/auth.config';

interface LocalAuthWrapperProps {
  children: React.ReactNode;
}

export const LocalAuthWrapper: React.FC<LocalAuthWrapperProps> = ({ children }) => {
  const { instance } = useMsal();
  const isLocalDev = (import.meta as any).env?.DEV && (
    window.location.hostname === 'localhost' || 
    window.location.hostname === '127.0.0.1'
  );

  const handleRedirect = () => {
    instance
      .loginRedirect({
        ...loginRequest,
        prompt: 'create'
      })
      .catch(console.error);
  };

  // In local development, bypass authentication
  if (isLocalDev) {
    return <>{children}</>;
  }

  // In production/dev environments, use proper authentication
  return (
    <>
      <AuthenticatedTemplate>
        {children}
      </AuthenticatedTemplate>
      <UnauthenticatedTemplate>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '100vh',
          flexDirection: 'column',
          gap: '16px'
        }}>
          <h2>Please sign in to continue</h2>
          <Button type="primary" onClick={handleRedirect}>
            Sign In with Microsoft
          </Button>
        </div>
      </UnauthenticatedTemplate>
    </>
  );
}; 