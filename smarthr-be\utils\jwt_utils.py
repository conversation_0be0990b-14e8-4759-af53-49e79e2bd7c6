
import os
import requests
import jwt
from jwt.algorithms import RSAAlgorithm
from jwt.exceptions import InvalidTokenError

# Environment variables
# Ensure these are set in your environment or provide defaults
TENANT_ID = os.getenv("TENANT_ID", "19eee545-4131-45c6-9a60-1a17e5cc507d")
if not TENANT_ID:
    raise ValueError("TENANT_ID environment variable is not set")

# Expected values for validation
# These should match your Azure AD app registration settings
EXPECTED_CLIENT_ID = os.getenv("CLIENT_ID_SMART_HR", "d35b6143-5032-41ba-b03b-1dae6c2cda10")
if not EXPECTED_CLIENT_ID:
    raise ValueError("CLIENT_ID_SMART_HR environment variable is not set")

# JWKS URL and expected issuers/audiences
# These URLs are used to fetch the public keys for token validation
JWKS_URL = f"https://login.microsoftonline.com/{TENANT_ID}/discovery/v2.0/keys"
EXPECTED_ISSUERS = [
    f"https://sts.windows.net/{TENANT_ID}/",
    f"https://login.microsoftonline.com/{TENANT_ID}/v2.0"
]    
EXPECTED_AUDIENCES = [EXPECTED_CLIENT_ID, f"api://{EXPECTED_CLIENT_ID}"]


# Token verification
def verify_token(token: str, scope: str):
    # Get Microsoft's public keys
    jwks_url = f'https://login.microsoftonline.com/{TENANT_ID}/discovery/v2.0/keys'
    jwks = requests.get(jwks_url).json()
    try:
        # Validate token (throws if invalid)
        claims = jwt.decode(
            token,
            jwks,
            algorithms=["RS256"],
            audience=scope,
            issuer=f"https://login.microsoftonline.com/{TENANT_ID}/v2.0"
        )
        print("Token is valid. Claims:")
        print(claims)
        return claims
    except Exception as e:
        print(f"Token validation failed: {str(e)}")
        return None


# Validate token against multiple issuers and audiences
def validate_token(token: str) -> dict:
    unverified_header = jwt.get_unverified_header(token)
    kid = unverified_header['kid']

    signing_keys = get_signing_keys()
    if kid not in signing_keys:
        raise Exception("Signing key not found")

    public_key = signing_keys[kid]

    last_exception = None
    for issuer in EXPECTED_ISSUERS:
        for aud in EXPECTED_AUDIENCES:
            try:
                payload = jwt.decode(
                    token,
                    key=public_key,
                    algorithms=['RS256'],
                    audience=aud,
                    issuer=issuer
                )
                return payload
            except InvalidTokenError as e:
                last_exception = e
                continue
    print(f"Invalid token for all expected issuers and audiences: {last_exception}")
    return None


# Fetch and cache signing keys from JWKS endpoint
def get_signing_keys():
    jwks = requests.get(JWKS_URL).json()
    keys = {}
    for key in jwks['keys']:
        kid = key['kid']
        public_key = RSAAlgorithm.from_jwk(key)
        keys[kid] = public_key
    return keys
