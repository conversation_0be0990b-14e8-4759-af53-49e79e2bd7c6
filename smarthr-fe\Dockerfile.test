# Dockerfile for running Playwright E2E tests in containerized environment
FROM mcr.microsoft.com/playwright:v1.40.0-focal

# Set working directory
WORKDIR /app

# Set environment variables
ENV NODE_ENV=test
ENV CI=true
ENV PLAYWRIGHT_BROWSERS_PATH=/ms-playwright

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Copy package files
COPY package*.json ./

# Install Node.js dependencies
RUN npm ci --only=production=false

# Copy source code
COPY . .

# Install Playwright browsers (they should already be in the base image)
RUN npx playwright install --with-deps

# Create directories for test results
RUN mkdir -p test-results playwright-report

# Set permissions
RUN chmod -R 755 test-results playwright-report

# Expose port for debugging (optional)
EXPOSE 9323

# Default command to run tests
CMD ["npm", "run", "test:e2e"]
