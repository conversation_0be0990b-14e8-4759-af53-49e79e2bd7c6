#!/usr/bin/env python3
"""
Comprehensive test for the evaluation bug fix.
Tests various scenarios to ensure the fix works correctly.
"""

import sys
import os
sys.path.append('smarthr-be')

def create_mock_evaluation_result():
    """Create mock evaluation result for testing validation logic."""
    try:
        from models.interview import EvaluationResult, QuestionEvaluation, Seniority
        
        # Test case 1: The user's scenario - should be flagged as suspicious before fix
        print("🧪 Test Case 1: User's Original Scenario (Before Fix)")
        print("=" * 50)
        
        # This simulates what the LLM would return BEFORE the fix
        # (counting Expected Response sections as candidate responses)
        before_fix_result = EvaluationResult(
            overall_seniority=Seniority.SENIOR,  # Wrong - based on Expected Response content
            per_question=[
                QuestionEvaluation(
                    question_number=1,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.SENIOR,
                    explanation="Candidate demonstrated extensive experience with <PERSON><PERSON><PERSON>, architected multiple data solutions focusing on performance optimization and scalability"
                ),
                QuestionEvaluation(
                    question_number=2,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.SENIOR,
                    explanation="Candidate showed thorough analysis of business requirements and data relationships, implementing best practices for indexing"
                ),
                QuestionEvaluation(
                    question_number=3,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.SENIOR,
                    explanation="Candidate implemented comprehensive data governance framework with automated checks and balances"
                ),
                QuestionEvaluation(
                    question_number=4,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.SENIOR,
                    explanation="Candidate has extensive experience with ETL tools including DBT and custom Python scripts"
                ),
                QuestionEvaluation(
                    question_number=5,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.SENIOR,
                    explanation="Candidate prioritizes data security with multi-layered approach including encryption and access controls"
                ),
                QuestionEvaluation(
                    question_number=6,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.JUNIOR,
                    explanation="Candidate responded 'I don't know' showing limited knowledge in performance optimization"
                )
            ],
            percentage_of_match=83.33,  # 5/6 questions - WRONG interpretation
            explanation="Candidate shows strong technical expertise across most areas with only one knowledge gap"
        )
        
        # Test validation - this should detect the suspicious pattern
        from controllers.interview_controller import _validate_evaluation_result
        issues = _validate_evaluation_result(before_fix_result, 6, "transcript-based")
        
        print(f"Validation issues detected: {len(issues)}")
        for issue in issues:
            print(f"  ⚠️  {issue}")
        
        if any("Expected Response" in issue for issue in issues):
            print("✅ Validation successfully detected potential 'Expected Response:' misinterpretation!")
        else:
            print("❌ Validation did not detect the misinterpretation pattern")
        
        print("\n" + "="*50)
        
        # Test case 2: After fix - correct interpretation
        print("🧪 Test Case 2: After Fix (Correct Interpretation)")
        print("=" * 50)
        
        after_fix_result = EvaluationResult(
            overall_seniority=Seniority.JUNIOR,  # Correct - based on actual candidate response
            per_question=[
                QuestionEvaluation(
                    question_number=1,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.JUNIOR,
                    explanation="No response found in transcript - question not addressed by candidate"
                ),
                QuestionEvaluation(
                    question_number=2,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.JUNIOR,
                    explanation="No response found in transcript - question not addressed by candidate"
                ),
                QuestionEvaluation(
                    question_number=3,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.JUNIOR,
                    explanation="No response found in transcript - question not addressed by candidate"
                ),
                QuestionEvaluation(
                    question_number=4,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.JUNIOR,
                    explanation="No response found in transcript - question not addressed by candidate"
                ),
                QuestionEvaluation(
                    question_number=5,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.JUNIOR,
                    explanation="No response found in transcript - question not addressed by candidate"
                ),
                QuestionEvaluation(
                    question_number=6,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.JUNIOR,
                    explanation="Candidate responded 'I don't know' showing limited knowledge in performance optimization"
                )
            ],
            percentage_of_match=16.67,  # 1/6 questions - CORRECT interpretation
            explanation="Candidate provided response to only one question, indicating limited technical knowledge"
        )
        
        # Test validation - this should have no issues
        issues_after = _validate_evaluation_result(after_fix_result, 6, "transcript-based")
        
        print(f"Validation issues detected: {len(issues_after)}")
        for issue in issues_after:
            print(f"  ⚠️  {issue}")
        
        if len(issues_after) == 0:
            print("✅ Validation passed - no suspicious patterns detected!")
        else:
            print("❌ Unexpected validation issues found")
        
        return True
        
    except ImportError as e:
        print(f"❌ Could not import required modules: {e}")
        return False
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_prompt_fix():
    """Test that the prompt fix is correctly applied."""
    print("\n🧪 Test Case 3: Prompt Fix Verification")
    print("=" * 40)
    
    try:
        # Read the fixed prompt from the controller
        with open('smarthr-be/controllers/interview_controller.py', 'r') as f:
            content = f.read()
        
        # Check that the problematic line is fixed
        if 'Look for responses following "Expected Response:" labels - these ARE the candidate\'s actual responses' in content:
            print("❌ CRITICAL: The problematic prompt instruction is still present!")
            return False
        
        # Check that the correct instruction is present
        if 'CRITICAL: "Expected Response:" sections are MODEL ANSWERS, NOT candidate responses - IGNORE these completely' in content:
            print("✅ Correct prompt instruction found!")
        else:
            print("❌ New prompt instruction not found")
            return False
        
        # Check for the enhanced examples
        if 'Expected Response: "React is a JavaScript library..." → IGNORE COMPLETELY' in content:
            print("✅ Enhanced examples with ignore instruction found!")
        else:
            print("❌ Enhanced examples not found")
            return False
        
        print("✅ Prompt fix verification passed!")
        return True
        
    except Exception as e:
        print(f"❌ Prompt verification failed: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Comprehensive Evaluation Bug Fix Test")
    print("Testing all aspects of the evaluation fix")
    print()
    
    success = True
    
    # Test 1: Validation logic
    if not create_mock_evaluation_result():
        success = False
    
    # Test 2: Prompt fix
    if not test_prompt_fix():
        success = False
    
    print("\n" + "="*60)
    if success:
        print("🎉 All tests passed! The evaluation bug fix is working correctly.")
        print("\nSummary of fixes:")
        print("1. ✅ Fixed prompt to ignore 'Expected Response:' sections")
        print("2. ✅ Enhanced validation to detect misinterpretation patterns")
        print("3. ✅ Added better examples in prompt instructions")
    else:
        print("❌ Some tests failed. Please review the issues above.")
        sys.exit(1)
