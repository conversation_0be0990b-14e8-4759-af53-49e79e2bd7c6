name: E2E Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run tests daily at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch:
    inputs:
      test_pattern:
        description: 'Test pattern to run (optional)'
        required: false
        default: ''
      browser:
        description: 'Browser to test'
        required: false
        default: 'all'
        type: choice
        options:
          - all
          - chromium
          - firefox
          - webkit

env:
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.11'

jobs:
  # Job to run E2E tests
  e2e-tests:
    name: E2E Tests (${{ matrix.browser }})
    runs-on: ubuntu-latest
    timeout-minutes: 60
    
    strategy:
      fail-fast: false
      matrix:
        browser: [chromium, firefox, webkit]
        
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: smarthr-fe/package-lock.json
          
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          
      - name: Install frontend dependencies
        working-directory: smarthr-fe
        run: npm ci
        
      - name: Install backend dependencies
        working-directory: smarthr-be
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          
      - name: Install Playwright browsers
        working-directory: smarthr-fe
        run: npx playwright install --with-deps ${{ matrix.browser }}
        
      - name: Setup PostgreSQL
        uses: harmon758/postgresql-action@v1
        with:
          postgresql version: '15'
          postgresql db: smarthr_test
          postgresql user: postgres
          postgresql password: password
          
      - name: Initialize test database
        working-directory: smarthr-fe
        run: |
          PGPASSWORD=password psql -h localhost -U postgres -d smarthr_test -f tests/fixtures/test-db-init.sql
          
      - name: Start backend server
        working-directory: smarthr-be
        run: |
          export DATABASE_URL="postgresql://postgres:password@localhost:5432/smarthr_test"
          export JWT_SECRET="test-secret-key"
          export ENVIRONMENT="test"
          python -m uvicorn main:app --host 0.0.0.0 --port 8080 &
          sleep 10
          
      - name: Start frontend server
        working-directory: smarthr-fe
        run: |
          export VITE_API_BASE_URL="http://localhost:8080"
          npm run dev &
          sleep 15
          
      - name: Wait for services
        run: |
          # Wait for backend
          timeout 60 bash -c 'until curl -f http://localhost:8080/health 2>/dev/null; do sleep 2; done'
          # Wait for frontend
          timeout 60 bash -c 'until curl -f http://localhost:5173 2>/dev/null; do sleep 2; done'
          
      - name: Run Playwright tests
        working-directory: smarthr-fe
        run: |
          if [ "${{ github.event.inputs.browser }}" != "all" ] && [ "${{ github.event.inputs.browser }}" != "${{ matrix.browser }}" ]; then
            echo "Skipping ${{ matrix.browser }} as ${{ github.event.inputs.browser }} was requested"
            exit 0
          fi

          if [ "${{ github.event.inputs.test_pattern }}" != "" ]; then
            npx playwright test --project=${{ matrix.browser }} --grep="${{ github.event.inputs.test_pattern }}"
          else
            npx playwright test --project=${{ matrix.browser }}
          fi
        env:
          CI: true
          
      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: playwright-results-${{ matrix.browser }}
          path: |
            smarthr-fe/test-results/
            smarthr-fe/playwright-report/
          retention-days: 30
          
      - name: Upload HTML report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: playwright-report-${{ matrix.browser }}
          path: smarthr-fe/playwright-report/
          retention-days: 30

  # Job for visual regression tests
  visual-regression:
    name: Visual Regression Tests
    runs-on: ubuntu-latest
    timeout-minutes: 30
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: smarthr-fe/package-lock.json
          
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          
      - name: Install frontend dependencies
        working-directory: smarthr-fe
        run: npm ci
        
      - name: Install backend dependencies
        working-directory: smarthr-be
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          
      - name: Install Playwright browsers
        working-directory: smarthr-fe
        run: npx playwright install --with-deps chromium
        
      - name: Setup PostgreSQL
        uses: harmon758/postgresql-action@v1
        with:
          postgresql version: '15'
          postgresql db: smarthr_test
          postgresql user: postgres
          postgresql password: password
          
      - name: Initialize test database
        working-directory: smarthr-fe
        run: |
          PGPASSWORD=password psql -h localhost -U postgres -d smarthr_test -f tests/fixtures/test-db-init.sql
          
      - name: Start backend server
        working-directory: smarthr-be
        run: |
          export DATABASE_URL="postgresql://postgres:password@localhost:5432/smarthr_test"
          export JWT_SECRET="test-secret-key"
          export ENVIRONMENT="test"
          python -m uvicorn main:app --host 0.0.0.0 --port 8080 &
          sleep 10
          
      - name: Start frontend server
        working-directory: smarthr-fe
        run: |
          export VITE_API_BASE_URL="http://localhost:8080"
          npm run dev &
          sleep 15
          
      - name: Wait for services
        run: |
          timeout 60 bash -c 'until curl -f http://localhost:8080/health 2>/dev/null; do sleep 2; done'
          timeout 60 bash -c 'until curl -f http://localhost:5173 2>/dev/null; do sleep 2; done'
          
      - name: Run visual regression tests
        working-directory: smarthr-fe
        run: npm run test:visual
        env:
          CI: true
          
      - name: Upload visual test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: visual-regression-results
          path: |
            smarthr-fe/test-results/
            smarthr-fe/playwright-report/
          retention-days: 30

  # Job to publish test results
  publish-results:
    name: Publish Test Results
    runs-on: ubuntu-latest
    needs: [e2e-tests, visual-regression]
    if: always()
    
    steps:
      - name: Download all artifacts
        uses: actions/download-artifact@v4
        
      - name: Publish test results
        uses: dorny/test-reporter@v1
        if: always()
        with:
          name: Playwright Test Results
          path: '**/results.json'
          reporter: 'java-junit'
          fail-on-error: true
          
      - name: Comment PR with test results
        uses: actions/github-script@v7
        if: github.event_name == 'pull_request' && always()
        with:
          script: |
            const fs = require('fs');
            const path = require('path');
            
            // Find summary files
            const summaryFiles = [];
            const findSummaryFiles = (dir) => {
              const files = fs.readdirSync(dir);
              for (const file of files) {
                const filePath = path.join(dir, file);
                if (fs.statSync(filePath).isDirectory()) {
                  findSummaryFiles(filePath);
                } else if (file === 'summary.json') {
                  summaryFiles.push(filePath);
                }
              }
            };
            
            try {
              findSummaryFiles('.');
              
              let comment = '## 🎭 Playwright Test Results\n\n';
              
              for (const summaryFile of summaryFiles) {
                const summary = JSON.parse(fs.readFileSync(summaryFile, 'utf8'));
                const browser = path.dirname(summaryFile).split('/').pop();
                
                comment += `### ${browser}\n`;
                comment += `- **Total Tests:** ${summary.totals.total}\n`;
                comment += `- **Passed:** ${summary.totals.passed} ✅\n`;
                comment += `- **Failed:** ${summary.totals.failed} ❌\n`;
                comment += `- **Pass Rate:** ${summary.totals.passRate}%\n`;
                comment += `- **Duration:** ${Math.round(summary.duration / 1000)}s\n\n`;
                
                if (summary.failedTests.length > 0) {
                  comment += `#### Failed Tests:\n`;
                  for (const test of summary.failedTests.slice(0, 5)) {
                    comment += `- ${test.title}\n`;
                  }
                  if (summary.failedTests.length > 5) {
                    comment += `- ... and ${summary.failedTests.length - 5} more\n`;
                  }
                  comment += '\n';
                }
              }
              
              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: comment
              });
            } catch (error) {
              console.log('Could not create test results comment:', error);
            }
