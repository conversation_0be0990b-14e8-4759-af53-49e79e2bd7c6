/**
 * Custom assertions and matchers for SmartHR E2E tests
 */

import { Page, Locator, expect } from '@playwright/test';

/**
 * Custom assertion helpers for SmartHR application
 */
export class SmartHRAssertions {
  private page: Page;

  constructor(page: Page) {
    this.page = page;
  }

  /**
   * Assert that a notification with specific text appears
   */
  async expectNotification(expectedText: string, timeout = 10000) {
    const notification = this.page.locator('.ant-message, .ant-notification');
    await expect(notification).toBeVisible({ timeout });
    await expect(notification).toContainText(expectedText);
  }

  /**
   * Assert that a success notification appears
   */
  async expectSuccessNotification(timeout = 10000) {
    const successNotification = this.page.locator('.ant-message-success, .ant-notification-notice-success');
    await expect(successNotification).toBeVisible({ timeout });
  }

  /**
   * Assert that an error notification appears
   */
  async expectErrorNotification(timeout = 10000) {
    const errorNotification = this.page.locator('.ant-message-error, .ant-notification-notice-error');
    await expect(errorNotification).toBeVisible({ timeout });
  }

  /**
   * Assert that a table has specific number of rows
   */
  async expectTableRowCount(selector: string, expectedCount: number) {
    const rows = this.page.locator(`${selector} tr`);
    await expect(rows).toHaveCount(expectedCount);
  }

  /**
   * Assert that a table contains a row with specific text
   */
  async expectTableToContainRow(tableSelector: string, rowText: string) {
    const table = this.page.locator(tableSelector);
    await expect(table).toContainText(rowText);
  }

  /**
   * Assert that a form field has a specific value
   */
  async expectFormFieldValue(fieldSelector: string, expectedValue: string) {
    const field = this.page.locator(fieldSelector);
    await expect(field).toHaveValue(expectedValue);
  }

  /**
   * Assert that a form field has an error
   */
  async expectFormFieldError(fieldSelector: string, errorText?: string) {
    const fieldContainer = this.page.locator(fieldSelector).locator('..').locator('..');
    const errorElement = fieldContainer.locator('.ant-form-item-explain-error, .ant-form-item-explain');
    await expect(errorElement).toBeVisible();
    
    if (errorText) {
      await expect(errorElement).toContainText(errorText);
    }
  }

  /**
   * Assert that a modal is open with specific title
   */
  async expectModalOpen(title?: string) {
    const modal = this.page.locator('.ant-modal');
    await expect(modal).toBeVisible();
    
    if (title) {
      const modalTitle = modal.locator('.ant-modal-title');
      await expect(modalTitle).toContainText(title);
    }
  }

  /**
   * Assert that a modal is closed
   */
  async expectModalClosed() {
    const modal = this.page.locator('.ant-modal');
    await expect(modal).not.toBeVisible();
  }

  /**
   * Assert that a drawer is open
   */
  async expectDrawerOpen(title?: string) {
    const drawer = this.page.locator('.ant-drawer');
    await expect(drawer).toBeVisible();
    
    if (title) {
      const drawerTitle = drawer.locator('.ant-drawer-title');
      await expect(drawerTitle).toContainText(title);
    }
  }

  /**
   * Assert that loading spinner is not visible
   */
  async expectLoadingComplete() {
    const spinner = this.page.locator('.ant-spin-spinning');
    await expect(spinner).not.toBeVisible();
  }

  /**
   * Assert that a button is disabled
   */
  async expectButtonDisabled(buttonSelector: string) {
    const button = this.page.locator(buttonSelector);
    await expect(button).toBeDisabled();
  }

  /**
   * Assert that a button is enabled
   */
  async expectButtonEnabled(buttonSelector: string) {
    const button = this.page.locator(buttonSelector);
    await expect(button).toBeEnabled();
  }

  /**
   * Assert that a tab is active
   */
  async expectTabActive(tabText: string) {
    const activeTab = this.page.locator('.ant-tabs-tab-active');
    await expect(activeTab).toContainText(tabText);
  }

  /**
   * Assert that a select has specific option selected
   */
  async expectSelectValue(selectSelector: string, expectedValue: string) {
    const select = this.page.locator(selectSelector);
    const selectedOption = select.locator('.ant-select-selection-item');
    await expect(selectedOption).toContainText(expectedValue);
  }

  /**
   * Assert that a checkbox is checked
   */
  async expectCheckboxChecked(checkboxSelector: string) {
    const checkbox = this.page.locator(checkboxSelector);
    await expect(checkbox).toBeChecked();
  }

  /**
   * Assert that a checkbox is unchecked
   */
  async expectCheckboxUnchecked(checkboxSelector: string) {
    const checkbox = this.page.locator(checkboxSelector);
    await expect(checkbox).not.toBeChecked();
  }

  /**
   * Assert that page URL matches expected pattern
   */
  async expectUrlToMatch(pattern: string | RegExp) {
    await expect(this.page).toHaveURL(pattern);
  }

  /**
   * Assert that page title matches expected text
   */
  async expectPageTitle(expectedTitle: string) {
    await expect(this.page).toHaveTitle(expectedTitle);
  }

  /**
   * Assert that an element has specific CSS class
   */
  async expectElementToHaveClass(selector: string, className: string) {
    const element = this.page.locator(selector);
    await expect(element).toHaveClass(new RegExp(className));
  }

  /**
   * Assert that an element has specific attribute
   */
  async expectElementToHaveAttribute(selector: string, attribute: string, value?: string) {
    const element = this.page.locator(selector);
    if (value) {
      await expect(element).toHaveAttribute(attribute, value);
    } else {
      await expect(element).toHaveAttribute(attribute);
    }
  }

  /**
   * Assert that text content matches expected value
   */
  async expectTextContent(selector: string, expectedText: string) {
    const element = this.page.locator(selector);
    await expect(element).toHaveText(expectedText);
  }

  /**
   * Assert that element contains specific text
   */
  async expectToContainText(selector: string, expectedText: string) {
    const element = this.page.locator(selector);
    await expect(element).toContainText(expectedText);
  }

  /**
   * Assert that element is visible within viewport
   */
  async expectElementInViewport(selector: string) {
    const element = this.page.locator(selector);
    await expect(element).toBeInViewport();
  }

  /**
   * Assert that API response has specific status
   */
  async expectApiResponse(urlPattern: string | RegExp, expectedStatus: number) {
    const response = await this.page.waitForResponse(urlPattern);
    expect(response.status()).toBe(expectedStatus);
  }

  /**
   * Assert that multiple elements are visible
   */
  async expectElementsVisible(selectors: string[]) {
    for (const selector of selectors) {
      const element = this.page.locator(selector);
      await expect(element).toBeVisible();
    }
  }

  /**
   * Assert that element count matches expected value
   */
  async expectElementCount(selector: string, expectedCount: number) {
    const elements = this.page.locator(selector);
    await expect(elements).toHaveCount(expectedCount);
  }

  /**
   * Assert that element has focus
   */
  async expectElementFocused(selector: string) {
    const element = this.page.locator(selector);
    await expect(element).toBeFocused();
  }

  /**
   * Assert that download was triggered
   */
  async expectDownloadTriggered(action: () => Promise<void>, expectedFilename?: string) {
    const downloadPromise = this.page.waitForEvent('download');
    await action();
    const download = await downloadPromise;
    
    if (expectedFilename) {
      expect(download.suggestedFilename()).toBe(expectedFilename);
    }
    
    return download;
  }
}

/**
 * Create assertion helper for a page
 */
export function createAssertions(page: Page): SmartHRAssertions {
  return new SmartHRAssertions(page);
}

/**
 * Global assertion utilities
 */
export const assertions = {
  /**
   * Assert that two arrays have the same elements (order doesn't matter)
   */
  expectArraysToHaveSameElements<T>(actual: T[], expected: T[]) {
    expect(actual.sort()).toEqual(expected.sort());
  },

  /**
   * Assert that object has specific properties
   */
  expectObjectToHaveProperties(obj: any, properties: string[]) {
    for (const prop of properties) {
      expect(obj).toHaveProperty(prop);
    }
  },

  /**
   * Assert that string matches email format
   */
  expectValidEmail(email: string) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    expect(email).toMatch(emailRegex);
  },

  /**
   * Assert that date is within expected range
   */
  expectDateInRange(date: Date, startDate: Date, endDate: Date) {
    expect(date.getTime()).toBeGreaterThanOrEqual(startDate.getTime());
    expect(date.getTime()).toBeLessThanOrEqual(endDate.getTime());
  },

  /**
   * Assert that number is within expected range
   */
  expectNumberInRange(value: number, min: number, max: number) {
    expect(value).toBeGreaterThanOrEqual(min);
    expect(value).toBeLessThanOrEqual(max);
  },
};
