import os
import logging

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Importar configuración de Azure Monitor para OpenTelemetry
# from azure.monitor.opentelemetry import configure_azure_monitor

from opentelemetry import trace
from opentelemetry._logs import set_logger_provider
from opentelemetry.sdk._logs import (
    <PERSON>ggerProvider,
    LoggingHandler,
)
from opentelemetry.sdk._logs.export import BatchLogRecordProcessor
from opentelemetry.sdk.trace import TracerProvider

from azure.monitor.opentelemetry.exporter import AzureMonitorLogExporter
trace.set_tracer_provider(TracerProvider())
tracer = trace.get_tracer(__name__)
logger_provider = LoggerProvider()
set_logger_provider(logger_provider)

exporter = AzureMonitorLogExporter(
    connection_string=os.environ["APPLICATIONINSIGHTS_CONNECTION_STRING"]
)

logger_provider.add_log_record_processor(BatchLogRecordProcessor(exporter))

# Configurar el logger de Python (los logs se enviarán a App Insights por OpenTelemetry)
handler = LoggingHandler()
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.addHandler(handler)
logger.setLevel(logging.DEBUG)

app = FastAPI(title="Semantic Matching Service")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["Content-Disposition"],
)


# Importar tu router después de configurar la instrumentación
from routes.routes import router as api_router
from routes.routes_interview import router as api_router_interview
from routes.routes_candidate import router as api_router_candidate
from routes.routes_position import router as api_router_position
from routes.routes_project import router as api_router_project
from routes.routes_note import router as api_router_note
from routes.routes_websocket import router as api_router_websocket

app.include_router(api_router)
app.include_router(api_router_candidate, prefix="/candidate", tags=["candidate"])
app.include_router(api_router_interview, prefix="/interview", tags=["interview"])
app.include_router(api_router_position, prefix="/position", tags=["position"])
app.include_router(api_router_project, prefix="/project", tags=["project"])
app.include_router(api_router_note, prefix="/note", tags=["note"])
app.include_router(api_router_websocket, prefix="/ws", tags=["websocket"])

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8080)
