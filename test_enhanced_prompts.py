#!/usr/bin/env python3
"""
Test script to verify enhanced interview evaluation prompts handle poor responses correctly.
This script tests the key issue: "i dont know" responses should be classified as answered (junior) not unanswered.
"""

import sys
import os

# Add the smarthr-be directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'smarthr-be'))

from models.interview import (
    EvaluationResult,
    QuestionEvaluation,
    Seniority,
    TranscriptQuestions,
    TranscriptQuestion
)

def test_enhanced_prompts():
    """Test the enhanced prompts with sample data."""
    
    print("🧪 Testing Enhanced Interview Evaluation Prompts")
    print("=" * 60)
    
    # Test Case 1: Validation function with poor responses
    print("\n1. Testing validation function with poor responses...")
    
    try:
        from controllers.interview_controller import _validate_evaluation_result
        
        # Create a result where poor responses are correctly classified as answered
        result = EvaluationResult(
            overall_seniority=Seniority.JUNIOR,
            per_question=[
                QuestionEvaluation(
                    question_number=1,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.JUNIOR,
                    explanation="Candidate responded 'I don't know' showing no experience with .NET Core"
                ),
                QuestionEvaluation(
                    question_number=2,
                    expected_seniority=Seniority.MID,
                    detected_seniority=Seniority.JUNIOR,
                    explanation="Candidate said 'Never used microservices' indicating limited experience"
                ),
                QuestionEvaluation(
                    question_number=3,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.JUNIOR,
                    explanation="Response was 'i dont know' - basic understanding only"
                )
            ],
            percentage_of_match=100.0,  # All 3 questions were answered (even poorly)
            explanation="Overall junior level due to limited experience in most areas"
        )
        
        issues = _validate_evaluation_result(result, 3, "test")
        
        if len(issues) == 0:
            print("✅ PASS: Poor responses correctly classified as answered questions")
        else:
            print(f"❌ FAIL: Validation detected issues: {issues}")
            
    except ImportError as e:
        print(f"❌ FAIL: Could not import validation function: {e}")
        return False
    except Exception as e:
        print(f"❌ FAIL: Error testing validation: {e}")
        return False
    
    # Test Case 2: Validation function detects misclassification
    print("\n2. Testing validation detects misclassification...")
    
    try:
        # Create a result where poor responses are incorrectly treated as unanswered
        bad_result = EvaluationResult(
            overall_seniority=Seniority.JUNIOR,
            per_question=[
                QuestionEvaluation(
                    question_number=1,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.JUNIOR,
                    explanation="Candidate responded 'I don't know' but was marked as unanswered"
                ),
                QuestionEvaluation(
                    question_number=2,
                    expected_seniority=Seniority.MID,
                    detected_seniority=Seniority.JUNIOR,
                    explanation="No response found in transcript"
                )
            ],
            percentage_of_match=50.0,  # Only 1 out of 2 marked as answered (incorrect)
            explanation="Limited responses from candidate"
        )
        
        issues = _validate_evaluation_result(bad_result, 2, "test")
        
        if len(issues) > 0 and any("misclassification" in issue.lower() for issue in issues):
            print("✅ PASS: Validation correctly detected misclassification")
        else:
            print(f"❌ FAIL: Validation should have detected misclassification. Issues: {issues}")
            
    except Exception as e:
        print(f"❌ FAIL: Error testing misclassification detection: {e}")
        return False
    
    # Test Case 3: Various poor response patterns
    print("\n3. Testing various poor response patterns...")
    
    poor_responses = [
        "i dont know",
        "I don't know",
        "not sure",
        "no experience",
        "never used it",
        "don't have experience",
        "unfamiliar with that"
    ]
    
    all_passed = True
    for response in poor_responses:
        try:
            result = EvaluationResult(
                overall_seniority=Seniority.JUNIOR,
                per_question=[
                    QuestionEvaluation(
                        question_number=1,
                        expected_seniority=Seniority.SENIOR,
                        detected_seniority=Seniority.JUNIOR,
                        explanation=f"Candidate responded '{response}' showing limited knowledge"
                    )
                ],
                percentage_of_match=100.0,  # Should be 100% since question was answered
                explanation="Junior level due to limited experience"
            )
            
            issues = _validate_evaluation_result(result, 1, "test")
            
            if len(issues) == 0:
                print(f"✅ PASS: '{response}' correctly classified as answered")
            else:
                print(f"❌ FAIL: '{response}' validation issues: {issues}")
                all_passed = False
                
        except Exception as e:
            print(f"❌ FAIL: Error testing '{response}': {e}")
            all_passed = False
    
    if not all_passed:
        return False
    
    # Test Case 4: Percentage calculation accuracy
    print("\n4. Testing percentage calculation accuracy...")
    
    test_cases = [
        (10, 10, 100.0),  # All answered
        (8, 10, 80.0),    # 8 out of 10
        (5, 10, 50.0),    # Half answered
        (0, 10, 0.0),     # None answered
    ]
    
    for answered, total, expected_pct in test_cases:
        try:
            per_question = []
            for i in range(total):
                if i < answered:
                    per_question.append(QuestionEvaluation(
                        question_number=i+1,
                        expected_seniority=Seniority.MID,
                        detected_seniority=Seniority.JUNIOR,
                        explanation="Candidate provided some response"
                    ))
                else:
                    per_question.append(QuestionEvaluation(
                        question_number=i+1,
                        expected_seniority=Seniority.MID,
                        detected_seniority=Seniority.JUNIOR,
                        explanation="No response found in transcript"
                    ))
            
            result = EvaluationResult(
                overall_seniority=Seniority.JUNIOR,
                per_question=per_question,
                percentage_of_match=expected_pct,
                explanation=f"Test case: {answered}/{total} questions answered"
            )
            
            issues = _validate_evaluation_result(result, total, "test")
            percentage_issues = [issue for issue in issues if "percentage" in issue.lower()]
            
            if len(percentage_issues) == 0:
                print(f"✅ PASS: {answered}/{total} = {expected_pct}% calculation correct")
            else:
                print(f"❌ FAIL: {answered}/{total} percentage issues: {percentage_issues}")
                return False
                
        except Exception as e:
            print(f"❌ FAIL: Error testing {answered}/{total}: {e}")
            return False
    
    print("\n🎉 All tests passed! Enhanced prompts should correctly handle poor responses.")
    return True

def test_prompt_content():
    """Test that the enhanced prompts contain the expected improvements."""
    
    print("\n5. Testing prompt content improvements...")
    
    try:
        # Read the interview controller file to check prompt content
        with open('smarthr-be/controllers/interview_controller.py', 'r') as f:
            content = f.read()
        
        # Check for key improvements in prompts
        improvements = [
            "RESPONSE CLASSIFICATION RULES",
            "i don't know",
            "ANSWERED (rate as 'junior')",
            "UNANSWERED: Complete silence",
            "EXAMPLES FOR CLARITY"
        ]
        
        all_found = True
        for improvement in improvements:
            if improvement in content:
                print(f"✅ FOUND: '{improvement}' in prompts")
            else:
                print(f"❌ MISSING: '{improvement}' not found in prompts")
                all_found = False
        
        return all_found
        
    except Exception as e:
        print(f"❌ FAIL: Error checking prompt content: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Enhanced Interview Evaluation Tests")
    print("=" * 60)
    
    try:
        # Test the validation logic
        validation_passed = test_enhanced_prompts()
        
        # Test the prompt content
        content_passed = test_prompt_content()
        
        if validation_passed and content_passed:
            print("\n🎉 ALL TESTS PASSED!")
            print("The enhanced prompts should correctly classify poor responses like 'i dont know' as answered questions.")
            sys.exit(0)
        else:
            print("\n❌ SOME TESTS FAILED!")
            print("Please review the issues above.")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 UNEXPECTED ERROR: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
