import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from './BasePage';
import { waitForApiCalls } from '../utils/test-helpers';

/**
 * Page Object Model for the Candidates page (/candidates)
 */
export class CandidatesPage extends BasePage {
  
  constructor(page: Page) {
    super(page);
  }

  getUrl(): string {
    return '/candidates';
  }

  async waitForPageLoad(): Promise<void> {
    await this.page.waitForSelector('.ant-table, [data-testid="candidates-table"]', { timeout: 30000 });
    await this.waitForTableData();
  }

  /**
   * Page elements
   */
  get searchInput() {
    return this.page.locator('input[placeholder*="Search"], .ant-input[placeholder*="search"]');
  }

  get addCandidateButton() {
    return this.page.locator('.ant-btn:has-text("Add"), button:has-text("Add")');
  }

  get candidatesTable() {
    return this.page.locator('.ant-table-tbody');
  }

  get candidateRows() {
    return this.page.locator('.ant-table-tbody tr');
  }

  get filterButton() {
    return this.page.locator('.ant-btn:has-text("Filter"), button:has-text("Filter")');
  }

  get exportButton() {
    return this.page.locator('.ant-btn:has-text("Export"), button:has-text("Export")');
  }

  get roleFilter() {
    return this.page.locator('[data-testid="role-filter"], .ant-select:has(.ant-select-selection-item:has-text("Role"))');
  }

  get locationFilter() {
    return this.page.locator('[data-testid="location-filter"], .ant-select:has(.ant-select-selection-item:has-text("Location"))');
  }

  get experienceFilter() {
    return this.page.locator('[data-testid="experience-filter"], .ant-select:has(.ant-select-selection-item:has-text("Experience"))');
  }

  /**
   * Actions
   */
  async searchForCandidate(searchTerm: string) {
    await this.searchInput.fill(searchTerm);
    await this.page.keyboard.press('Enter');
    await waitForApiCalls(this.page);
    await this.waitForTableData();
  }

  async clearSearch() {
    await this.searchInput.clear();
    await this.page.keyboard.press('Enter');
    await waitForApiCalls(this.page);
    await this.waitForTableData();
  }

  async clickAddCandidate() {
    await this.addCandidateButton.click();
    await this.page.waitForSelector('.ant-modal, .ant-drawer', { timeout: 10000 });
  }

  async filterByRole(role: string) {
    await this.roleFilter.click();
    await this.page.waitForSelector('.ant-select-dropdown');
    await this.page.click(`.ant-select-item-option:has-text("${role}")`);
    await this.page.waitForSelector('.ant-select-dropdown', { state: 'hidden' });
  }

  async filterByLocation(location: string) {
    await this.locationFilter.click();
    await this.page.waitForSelector('.ant-select-dropdown');
    await this.page.click(`.ant-select-item-option:has-text("${location}")`);
    await this.page.waitForSelector('.ant-select-dropdown', { state: 'hidden' });
  }

  async filterByExperience(experience: string) {
    await this.experienceFilter.click();
    await this.page.waitForSelector('.ant-select-dropdown');
    await this.page.click(`.ant-select-item-option:has-text("${experience}")`);
    await this.page.waitForSelector('.ant-select-dropdown', { state: 'hidden' });
  }

  async clickCandidateRow(candidateName: string) {
    const row = this.page.locator(`.ant-table-tbody tr:has-text("${candidateName}")`);
    await row.click();
    await waitForApiCalls(this.page);
  }

  async getCandidateRowByName(candidateName: string) {
    return this.page.locator(`.ant-table-tbody tr:has-text("${candidateName}")`);
  }

  async viewCandidateDetails(candidateName: string) {
    const row = this.getCandidateRowByName(candidateName);
    const viewButton = row.locator('.ant-btn:has-text("View"), button:has-text("View")');
    await viewButton.click();
    await waitForApiCalls(this.page);
  }

  async editCandidate(candidateName: string) {
    const row = this.getCandidateRowByName(candidateName);
    const editButton = row.locator('.ant-btn:has-text("Edit"), button:has-text("Edit")');
    await editButton.click();
    await this.page.waitForSelector('.ant-modal, .ant-drawer');
  }

  async deleteCandidate(candidateName: string) {
    const row = this.getCandidateRowByName(candidateName);
    const deleteButton = row.locator('.ant-btn:has-text("Delete"), button:has-text("Delete")');
    await deleteButton.click();
    
    // Confirm deletion in modal
    await this.page.waitForSelector('.ant-modal');
    await this.page.click('.ant-btn-primary:has-text("Confirm"), .ant-btn-primary:has-text("Delete")');
    await waitForApiCalls(this.page);
  }

  /**
   * Assertions
   */
  async expectCandidateToBeVisible(candidateName: string) {
    const candidateRow = this.getCandidateRowByName(candidateName);
    await expect(candidateRow).toBeVisible();
  }

  async expectCandidateNotToBeVisible(candidateName: string) {
    const candidateRow = this.getCandidateRowByName(candidateName);
    await expect(candidateRow).not.toBeVisible();
  }

  async expectTableToHaveRows(minCount = 1) {
    await expect(this.candidateRows).toHaveCount({ min: minCount });
  }

  async expectEmptyTable() {
    const emptyMessage = this.page.locator('.ant-empty, .ant-table-placeholder');
    await expect(emptyMessage).toBeVisible();
  }

  async getCandidateCount(): Promise<number> {
    await this.waitForTableData();
    return await this.candidateRows.count();
  }

  async getCandidateNames(): Promise<string[]> {
    await this.waitForTableData();
    const rows = await this.candidateRows.all();
    const names: string[] = [];
    
    for (const row of rows) {
      const nameCell = row.locator('td').first();
      const name = await nameCell.textContent();
      if (name) {
        names.push(name.trim());
      }
    }
    
    return names;
  }

  /**
   * Wait for specific candidate to appear in the table
   */
  async waitForCandidateToAppear(candidateName: string, timeout = 10000) {
    await this.page.waitForFunction(
      (name) => {
        const rows = document.querySelectorAll('.ant-table-tbody tr');
        return Array.from(rows).some(row => row.textContent?.includes(name));
      },
      candidateName,
      { timeout }
    );
  }

  /**
   * Get candidate details from table row
   */
  async getCandidateDetails(candidateName: string) {
    const row = this.getCandidateRowByName(candidateName);
    await expect(row).toBeVisible();
    
    const cells = row.locator('td');
    const cellCount = await cells.count();
    
    const details: Record<string, string> = {};
    for (let i = 0; i < cellCount; i++) {
      const cell = cells.nth(i);
      const text = await cell.textContent();
      details[`column_${i}`] = text?.trim() || '';
    }
    
    return details;
  }

  /**
   * Pagination
   */
  async goToNextPage() {
    const nextButton = this.page.locator('.ant-pagination-next');
    await nextButton.click();
    await waitForApiCalls(this.page);
    await this.waitForTableData();
  }

  async goToPreviousPage() {
    const prevButton = this.page.locator('.ant-pagination-prev');
    await prevButton.click();
    await waitForApiCalls(this.page);
    await this.waitForTableData();
  }

  async goToPage(pageNumber: number) {
    const pageButton = this.page.locator(`.ant-pagination-item-${pageNumber}`);
    await pageButton.click();
    await waitForApiCalls(this.page);
    await this.waitForTableData();
  }

  async getCurrentPage(): Promise<number> {
    const activePageElement = this.page.locator('.ant-pagination-item-active');
    const pageText = await activePageElement.textContent();
    return parseInt(pageText?.trim() || '1');
  }
}
