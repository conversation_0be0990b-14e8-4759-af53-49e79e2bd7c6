/**
 * Test data generators and fixtures for SmartHR E2E tests
 */

// Using simple random data generation instead of faker for now
// TODO: Install @faker-js/faker if more sophisticated data generation is needed

export interface TestPosition {
  id?: string;
  positionName: string;
  clientName: string;
  jobDescription: string;
  mainResponsabilities: string;
  seniority: 'junior' | 'mid' | 'senior';
  roleName: string;
  projectName: string;
  location: string;
  salaryRange: string;
  requiredSkills: string[];
  preferredSkills: string[];
}

export interface TestCandidate {
  id?: string;
  personalInfo: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    location: string;
  };
  professionalInfo: {
    currentRole: string;
    experience: string;
    skills: string[];
    education: string;
  };
}

export interface TestInterview {
  id?: string;
  positionId: string;
  candidateId: string;
  status: 'not_scheduled' | 'scheduled' | 'completed' | 'cancelled';
  type: 'hr' | 'technical';
  scheduledDate?: string;
  feedback?: any;
  transcript?: string;
}

/**
 * Generate a test position
 */
export function generateTestPosition(overrides: Partial<TestPosition> = {}): TestPosition {
  const jobTitles = ['Software Engineer', 'Data Analyst', 'Product Manager', 'UX Designer', 'DevOps Engineer'];
  const companies = ['TechCorp', 'DataSoft', 'InnovateLab', 'CloudSystems', 'AgileWorks'];
  const locations = ['New York', 'San Francisco', 'Austin', 'Seattle', 'Boston'];
  const roles = ['Engineering', 'Data Science', 'Product', 'Design', 'Operations'];
  const projects = ['Platform Modernization', 'Data Pipeline', 'Mobile App', 'Analytics Dashboard', 'Cloud Migration'];

  return {
    positionName: jobTitles[Math.floor(Math.random() * jobTitles.length)],
    clientName: companies[Math.floor(Math.random() * companies.length)],
    jobDescription: 'We are looking for a talented professional to join our team and contribute to exciting projects.',
    mainResponsabilities: 'Lead development initiatives, collaborate with cross-functional teams, and deliver high-quality solutions.',
    seniority: ['junior', 'mid', 'senior'][Math.floor(Math.random() * 3)] as 'junior' | 'mid' | 'senior',
    roleName: roles[Math.floor(Math.random() * roles.length)],
    projectName: projects[Math.floor(Math.random() * projects.length)],
    location: locations[Math.floor(Math.random() * locations.length)],
    salaryRange: `$${50000 + Math.floor(Math.random() * 100000)} - $${150000 + Math.floor(Math.random() * 100000)}`,
    requiredSkills: ['JavaScript', 'TypeScript', 'React', 'Node.js', 'Python'].slice(0, 3 + Math.floor(Math.random() * 3)),
    preferredSkills: ['GraphQL', 'Redis', 'Docker'].slice(0, 1 + Math.floor(Math.random() * 2)),
    ...overrides
  };
}

/**
 * Generate a test candidate
 */
export function generateTestCandidate(overrides: Partial<TestCandidate> = {}): TestCandidate {
  const firstNames = ['John', 'Jane', 'Michael', 'Sarah', 'David', 'Emily', 'Robert', 'Lisa'];
  const lastNames = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis'];
  const locations = ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix', 'Philadelphia'];
  const roles = ['Software Developer', 'Data Analyst', 'Product Manager', 'UX Designer', 'DevOps Engineer'];
  const educations = [
    'Bachelor in Computer Science',
    'Master in Software Engineering',
    'Bachelor in Information Technology',
    'Self-taught Developer'
  ];

  const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
  const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];

  return {
    personalInfo: {
      firstName,
      lastName,
      email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}@email.com`,
      phone: `******-${Math.floor(Math.random() * 900) + 100}-${Math.floor(Math.random() * 9000) + 1000}`,
      location: locations[Math.floor(Math.random() * locations.length)],
    },
    professionalInfo: {
      currentRole: roles[Math.floor(Math.random() * roles.length)],
      experience: `${1 + Math.floor(Math.random() * 14)} years`,
      skills: ['JavaScript', 'TypeScript', 'React', 'Node.js', 'Python', 'Java'].slice(0, 4 + Math.floor(Math.random() * 4)),
      education: educations[Math.floor(Math.random() * educations.length)],
    },
    ...overrides
  };
}

/**
 * Generate test interview data
 */
export function generateTestInterview(overrides: Partial<TestInterview> = {}): TestInterview {
  const statuses = ['not_scheduled', 'scheduled', 'completed', 'cancelled'];
  const types = ['hr', 'technical'];

  return {
    positionId: crypto.randomUUID(),
    candidateId: crypto.randomUUID(),
    status: statuses[Math.floor(Math.random() * statuses.length)] as any,
    type: types[Math.floor(Math.random() * types.length)] as 'hr' | 'technical',
    scheduledDate: new Date(Date.now() + Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
    ...overrides
  };
}

/**
 * Generate interview questions
 */
export function generateInterviewQuestions(count = 5) {
  const sampleQuestions = [
    'Can you explain your experience with React and modern JavaScript?',
    'How do you approach testing in your development workflow?',
    'Describe your experience with backend technologies.',
    'What is your preferred approach to state management?',
    'How do you handle error handling in your applications?'
  ];

  const tags = ['Technical Skills', 'Soft Skills', 'Methodologies', 'Language - Tools'];

  return Array.from({ length: count }, (_, index) => ({
    question_number: index + 1,
    question: sampleQuestions[index % sampleQuestions.length],
    tag: tags[Math.floor(Math.random() * tags.length)],
    junior_answer: 'I have basic understanding and some hands-on experience with this topic.',
    mid_answer: 'I have solid practical experience and can implement solutions effectively.',
    senior_answer: 'I have extensive experience and can architect scalable solutions while mentoring others.'
  }));
}

/**
 * Generate sample transcript
 */
export function generateSampleTranscript(): string {
  const interviewers = ['Sarah Johnson', 'Michael Chen', 'Emily Rodriguez', 'David Kim'];
  const candidates = ['Alex Thompson', 'Jordan Martinez', 'Taylor Wilson', 'Casey Brown'];

  return `
TECHNICAL INTERVIEW TRANSCRIPT
Date: ${new Date().toLocaleDateString()}
Interviewer: ${interviewers[Math.floor(Math.random() * interviewers.length)]}
Candidate: ${candidates[Math.floor(Math.random() * candidates.length)]}

INTERVIEWER: Good morning! Let's start with some technical questions.

TECHNICAL SKILLS
1. Can you explain your experience with React and modern JavaScript?

CANDIDATE: I have about 3 years of experience with React. I've worked on several projects using hooks, context API, and state management libraries like Redux. I'm comfortable with ES6+ features and have experience with TypeScript as well.

METHODOLOGIES
2. How do you approach testing in your development workflow?

CANDIDATE: I believe in test-driven development. I usually start with unit tests using Jest and React Testing Library, then move to integration tests. For E2E testing, I've used Cypress and Playwright.

TECHNICAL SKILLS
3. Describe your experience with backend technologies.

CANDIDATE: I've worked primarily with Node.js and Express, but I also have experience with Python and FastAPI. I'm comfortable with both SQL and NoSQL databases, particularly PostgreSQL and MongoDB.
  `.trim();
}

/**
 * Common test users
 */
export const TEST_USERS = {
  admin: {
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin'
  },
  recruiter: {
    email: '<EMAIL>',
    password: 'recruiter123',
    role: 'recruiter'
  },
  interviewer: {
    email: '<EMAIL>',
    password: 'interviewer123',
    role: 'interviewer'
  }
};

/**
 * API endpoints for testing
 */
export const TEST_ENDPOINTS = {
  positions: '/position/positions_pagination/',
  candidates: '/candidate/candidates_pagination/',
  interviews: '/interview',
  questions: '/interview/{id}/questions'
};
