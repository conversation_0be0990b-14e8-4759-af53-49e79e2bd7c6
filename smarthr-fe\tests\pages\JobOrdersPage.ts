import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from './BasePage';
import { waitForApiCalls } from '../utils/test-helpers';

/**
 * Page Object Model for the Job Orders page (/)
 */
export class JobOrdersPage extends BasePage {
  
  constructor(page: Page) {
    super(page);
  }

  getUrl(): string {
    return '/';
  }

  async waitForPageLoad(): Promise<void> {
    await this.page.waitForSelector('.ant-table, [data-testid="job-orders-table"]', { timeout: 30000 });
    await this.waitForTableData();
  }

  /**
   * Page elements
   */
  get searchInput() {
    return this.page.locator('input[placeholder*="Search"], .ant-input[placeholder*="search"]');
  }

  get filterButton() {
    return this.page.locator('.ant-btn:has-text("Filter"), button:has-text("Filter")');
  }

  get createJobButton() {
    return this.page.locator('.ant-btn:has-text("Create"), button:has-text("Create")');
  }

  get jobTable() {
    return this.page.locator('.ant-table-tbody');
  }

  get jobRows() {
    return this.page.locator('.ant-table-tbody tr');
  }

  get stageFilter() {
    return this.page.locator('[data-testid="stage-filter"], .ant-select:has(.ant-select-selection-item:has-text("Stage"))');
  }

  get clientFilter() {
    return this.page.locator('[data-testid="client-filter"], .ant-select:has(.ant-select-selection-item:has-text("Client"))');
  }

  get locationFilter() {
    return this.page.locator('[data-testid="location-filter"], .ant-select:has(.ant-select-selection-item:has-text("Location"))');
  }

  get applyFiltersButton() {
    return this.page.locator('.ant-btn:has-text("Apply"), button:has-text("Apply")');
  }

  get clearFiltersButton() {
    return this.page.locator('.ant-btn:has-text("Clear"), button:has-text("Clear")');
  }

  /**
   * Actions
   */
  async searchForJob(searchTerm: string) {
    await this.searchInput.fill(searchTerm);
    await this.page.keyboard.press('Enter');
    await waitForApiCalls(this.page);
    await this.waitForTableData();
  }

  async clearSearch() {
    await this.searchInput.clear();
    await this.page.keyboard.press('Enter');
    await waitForApiCalls(this.page);
    await this.waitForTableData();
  }

  async filterByStage(stage: string) {
    await this.stageFilter.click();
    await this.page.waitForSelector('.ant-select-dropdown');
    await this.page.click(`.ant-select-item-option:has-text("${stage}")`);
    await this.page.waitForSelector('.ant-select-dropdown', { state: 'hidden' });
  }

  async filterByClient(client: string) {
    await this.clientFilter.click();
    await this.page.waitForSelector('.ant-select-dropdown');
    await this.page.click(`.ant-select-item-option:has-text("${client}")`);
    await this.page.waitForSelector('.ant-select-dropdown', { state: 'hidden' });
  }

  async filterByLocation(location: string) {
    await this.locationFilter.click();
    await this.page.waitForSelector('.ant-select-dropdown');
    await this.page.click(`.ant-select-item-option:has-text("${location}")`);
    await this.page.waitForSelector('.ant-select-dropdown', { state: 'hidden' });
  }

  async applyFilters() {
    await this.applyFiltersButton.click();
    await waitForApiCalls(this.page);
    await this.waitForTableData();
  }

  async clearFilters() {
    await this.clearFiltersButton.click();
    await waitForApiCalls(this.page);
    await this.waitForTableData();
  }

  async clickJobRow(jobName: string) {
    const row = this.page.locator(`.ant-table-tbody tr:has-text("${jobName}")`);
    await row.click();
    await waitForApiCalls(this.page);
  }

  async getJobRowByName(jobName: string) {
    return this.page.locator(`.ant-table-tbody tr:has-text("${jobName}")`);
  }

  /**
   * Assertions
   */
  async expectJobToBeVisible(jobName: string) {
    const jobRow = this.getJobRowByName(jobName);
    await expect(jobRow).toBeVisible();
  }

  async expectJobNotToBeVisible(jobName: string) {
    const jobRow = this.getJobRowByName(jobName);
    await expect(jobRow).not.toBeVisible();
  }

  async expectTableToHaveRows(minCount = 1) {
    await expect(this.jobRows).toHaveCount({ min: minCount });
  }

  async expectEmptyTable() {
    const emptyMessage = this.page.locator('.ant-empty, .ant-table-placeholder');
    await expect(emptyMessage).toBeVisible();
  }

  async getJobCount(): Promise<number> {
    await this.waitForTableData();
    return await this.jobRows.count();
  }

  async getJobNames(): Promise<string[]> {
    await this.waitForTableData();
    const rows = await this.jobRows.all();
    const names: string[] = [];
    
    for (const row of rows) {
      const nameCell = row.locator('td').first();
      const name = await nameCell.textContent();
      if (name) {
        names.push(name.trim());
      }
    }
    
    return names;
  }

  /**
   * Wait for specific job to appear in the table
   */
  async waitForJobToAppear(jobName: string, timeout = 10000) {
    await this.page.waitForFunction(
      (name) => {
        const rows = document.querySelectorAll('.ant-table-tbody tr');
        return Array.from(rows).some(row => row.textContent?.includes(name));
      },
      jobName,
      { timeout }
    );
  }

  /**
   * Get job details from table row
   */
  async getJobDetails(jobName: string) {
    const row = this.getJobRowByName(jobName);
    await expect(row).toBeVisible();
    
    const cells = row.locator('td');
    const cellCount = await cells.count();
    
    const details: Record<string, string> = {};
    for (let i = 0; i < cellCount; i++) {
      const cell = cells.nth(i);
      const text = await cell.textContent();
      details[`column_${i}`] = text?.trim() || '';
    }
    
    return details;
  }
}
