version: '3.8'

services:
  # Frontend application
  smarthr-frontend:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "5173:5173"
    environment:
      - NODE_ENV=development
      - VITE_API_BASE_URL=http://smarthr-backend:8080
    volumes:
      - .:/app
      - /app/node_modules
    command: npm run dev
    networks:
      - smarthr-test-network

  # Backend application
  smarthr-backend:
    build:
      context: ../smarthr-be
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - DATABASE_URL=********************************************/smarthr_test
      - JWT_SECRET=test-secret-key
      - ENVIRONMENT=test
    depends_on:
      - postgres
    networks:
      - smarthr-test-network

  # PostgreSQL database
  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=smarthr_test
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
      - ./tests/fixtures/test-db-init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - smarthr-test-network

  # Playwright test runner
  playwright-tests:
    build:
      context: .
      dockerfile: Dockerfile.test
    environment:
      - NODE_ENV=test
      - CI=true
      - BASE_URL=http://smarthr-frontend:5173
      - API_BASE_URL=http://smarthr-backend:8080
      - DATABASE_URL=********************************************/smarthr_test
    volumes:
      - ./test-results:/app/test-results
      - ./playwright-report:/app/playwright-report
      - ./tests:/app/tests
    depends_on:
      - smarthr-frontend
      - smarthr-backend
      - postgres
    networks:
      - smarthr-test-network
    command: >
      sh -c "
        echo 'Waiting for services to be ready...' &&
        sleep 30 &&
        echo 'Running Playwright tests...' &&
        npm run test:e2e
      "

  # Test database setup service
  test-db-setup:
    image: postgres:15
    environment:
      - PGPASSWORD=password
    volumes:
      - ./tests/fixtures/test-data.sql:/test-data.sql
    networks:
      - smarthr-test-network
    depends_on:
      - postgres
    command: >
      sh -c "
        echo 'Waiting for PostgreSQL to be ready...' &&
        sleep 10 &&
        psql -h postgres -U postgres -d smarthr_test -f /test-data.sql &&
        echo 'Test data loaded successfully'
      "

volumes:
  postgres_test_data:

networks:
  smarthr-test-network:
    driver: bridge
