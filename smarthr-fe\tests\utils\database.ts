/**
 * Database utilities for E2E tests
 * These utilities help manage test data in the database
 */

import { TestPosition, TestCandidate, TestInterview } from '../fixtures/test-data';

const API_BASE_URL = process.env.PLAYWRIGHT_API_URL || 'http://localhost:8080';

/**
 * Database utility class for managing test data
 */
export class DatabaseUtils {
  private static instance: DatabaseUtils;
  private createdPositions: string[] = [];
  private createdCandidates: string[] = [];
  private createdInterviews: string[] = [];

  static getInstance(): DatabaseUtils {
    if (!DatabaseUtils.instance) {
      DatabaseUtils.instance = new DatabaseUtils();
    }
    return DatabaseUtils.instance;
  }

  /**
   * Create a test position in the database
   */
  async createPosition(positionData: TestPosition): Promise<string> {
    try {
      const response = await fetch(`${API_BASE_URL}/position/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          position_info: {
            positionName: positionData.positionName,
            clientName: positionData.clientName,
            jobDescription: positionData.jobDescription,
            mainResponsabilities: positionData.mainResponsabilities,
            seniority: positionData.seniority,
            roleName: positionData.roleName,
            projectName: positionData.projectName,
            location: positionData.location,
            salaryRange: positionData.salaryRange,
            requiredSkills: positionData.requiredSkills,
            preferredSkills: positionData.preferredSkills
          }
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to create position: ${response.statusText}`);
      }

      const result = await response.json();
      const positionId = result.id;
      this.createdPositions.push(positionId);
      return positionId;
    } catch (error) {
      console.error('Error creating test position:', error);
      throw error;
    }
  }

  /**
   * Create a test candidate in the database
   */
  async createCandidate(candidateData: TestCandidate): Promise<string> {
    try {
      const response = await fetch(`${API_BASE_URL}/candidate/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          candidate_info: {
            personal_info: candidateData.personalInfo,
            professional_info: candidateData.professionalInfo
          }
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to create candidate: ${response.statusText}`);
      }

      const result = await response.json();
      const candidateId = result.id;
      this.createdCandidates.push(candidateId);
      return candidateId;
    } catch (error) {
      console.error('Error creating test candidate:', error);
      throw error;
    }
  }

  /**
   * Create a test interview in the database
   */
  async createInterview(interviewData: TestInterview): Promise<string> {
    try {
      const response = await fetch(`${API_BASE_URL}/interview/${interviewData.positionId}?candidates_id=${interviewData.candidateId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to create interview: ${response.statusText}`);
      }

      const result = await response.json();
      // The API returns an array of interviews, get the first one
      const interviewId = result[0]?.id;
      if (interviewId) {
        this.createdInterviews.push(interviewId);
      }
      return interviewId;
    } catch (error) {
      console.error('Error creating test interview:', error);
      throw error;
    }
  }

  /**
   * Generate interview questions for a position
   */
  async generateQuestions(positionId: string, questionCount = 5): Promise<void> {
    try {
      const response = await fetch(`${API_BASE_URL}/interview/${positionId}/questions?n_questions=${questionCount}&include=Technical Skills,Methodologies&current_user=test-user`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to generate questions: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Error generating interview questions:', error);
      throw error;
    }
  }

  /**
   * Clean up all created test data
   */
  async cleanup(): Promise<void> {
    console.log('🧹 Cleaning up test data...');
    
    // Clean up interviews
    for (const interviewId of this.createdInterviews) {
      try {
        // Note: The API might not have a direct delete interview endpoint
        // This is a placeholder for cleanup logic
        console.log(`Cleaning up interview: ${interviewId}`);
      } catch (error) {
        console.warn(`Failed to cleanup interview ${interviewId}:`, error);
      }
    }

    // Clean up candidates
    for (const candidateId of this.createdCandidates) {
      try {
        await fetch(`${API_BASE_URL}/candidate/${candidateId}/delete`, {
          method: 'DELETE'
        });
      } catch (error) {
        console.warn(`Failed to cleanup candidate ${candidateId}:`, error);
      }
    }

    // Clean up positions
    for (const positionId of this.createdPositions) {
      try {
        await fetch(`${API_BASE_URL}/position/${positionId}`, {
          method: 'DELETE'
        });
      } catch (error) {
        console.warn(`Failed to cleanup position ${positionId}:`, error);
      }
    }

    // Reset tracking arrays
    this.createdPositions = [];
    this.createdCandidates = [];
    this.createdInterviews = [];
    
    console.log('✅ Test data cleanup completed');
  }

  /**
   * Get a position by ID
   */
  async getPosition(positionId: string): Promise<any> {
    try {
      const response = await fetch(`${API_BASE_URL}/position/${positionId}`);
      if (!response.ok) {
        throw new Error(`Failed to get position: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error getting position:', error);
      throw error;
    }
  }

  /**
   * Get a candidate by ID
   */
  async getCandidate(candidateId: string): Promise<any> {
    try {
      const response = await fetch(`${API_BASE_URL}/candidate/${candidateId}`);
      if (!response.ok) {
        throw new Error(`Failed to get candidate: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error getting candidate:', error);
      throw error;
    }
  }

  /**
   * Get interviews for a position
   */
  async getInterviews(positionId: string): Promise<any[]> {
    try {
      const response = await fetch(`${API_BASE_URL}/interview/${positionId}`);
      if (!response.ok) {
        throw new Error(`Failed to get interviews: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error getting interviews:', error);
      throw error;
    }
  }
}

/**
 * Convenience function to get database utils instance
 */
export const db = DatabaseUtils.getInstance();
