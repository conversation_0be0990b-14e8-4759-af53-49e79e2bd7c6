import { Page, BrowserContext } from '@playwright/test';
import { waitForAppLoad } from './test-helpers';

/**
 * Authentication utilities for E2E tests
 * Handles Azure MSAL authentication and session management
 */

export interface TestUser {
  email: string;
  password: string;
  role: string;
  displayName?: string;
}

export const TEST_USERS: Record<string, TestUser> = {
  admin: {
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin',
    displayName: 'Test Admin'
  },
  recruiter: {
    email: '<EMAIL>',
    password: 'recruiter123',
    role: 'recruiter',
    displayName: 'Test Recruiter'
  },
  interviewer: {
    email: '<EMAIL>',
    password: 'interviewer123',
    role: 'interviewer',
    displayName: 'Test Interviewer'
  }
};

/**
 * Authentication helper class
 */
export class AuthHelper {
  private page: Page;
  private context: BrowserContext;

  constructor(page: Page, context: BrowserContext) {
    this.page = page;
    this.context = context;
  }

  /**
   * Login with Azure MSAL (if authentication is enabled)
   */
  async loginWithMsal(user: TestUser) {
    try {
      // Navigate to the app
      await this.page.goto('/');
      
      // Check if we're already logged in
      if (await this.isLoggedIn()) {
        console.log('User is already logged in');
        return;
      }

      // Look for login button or redirect to MSAL
      const loginButton = this.page.locator('button:has-text("Login"), .ant-btn:has-text("Sign in")');
      
      if (await loginButton.isVisible({ timeout: 5000 })) {
        await loginButton.click();
        
        // Wait for MSAL redirect or login form
        await this.page.waitForLoadState('networkidle');
        
        // Handle MSAL login form if it appears
        await this.handleMsalLogin(user);
      } else {
        // If no login button, the app might be in development mode without auth
        console.log('No login required - app appears to be in development mode');
      }

      // Wait for the app to load after login
      await waitForAppLoad(this.page);
      
    } catch (error) {
      console.warn('MSAL login failed or not required:', error);
      // Continue with the test - the app might be running without authentication
    }
  }

  /**
   * Handle MSAL login form
   */
  private async handleMsalLogin(user: TestUser) {
    try {
      // Wait for MSAL login page to load
      await this.page.waitForSelector('input[type="email"], input[name="loginfmt"]', { timeout: 10000 });
      
      // Fill email
      await this.page.fill('input[type="email"], input[name="loginfmt"]', user.email);
      await this.page.click('input[type="submit"], button[type="submit"]');
      
      // Wait for password field
      await this.page.waitForSelector('input[type="password"], input[name="passwd"]', { timeout: 10000 });
      
      // Fill password
      await this.page.fill('input[type="password"], input[name="passwd"]', user.password);
      await this.page.click('input[type="submit"], button[type="submit"]');
      
      // Handle "Stay signed in?" prompt if it appears
      const staySignedInButton = this.page.locator('input[type="submit"]:has-text("Yes"), button:has-text("Yes")');
      if (await staySignedInButton.isVisible({ timeout: 5000 })) {
        await staySignedInButton.click();
      }
      
      // Wait for redirect back to the app
      await this.page.waitForURL('**/', { timeout: 30000 });
      
    } catch (error) {
      console.warn('MSAL form handling failed:', error);
      throw error;
    }
  }

  /**
   * Login for development/testing (bypass authentication)
   */
  async loginForTesting(user: TestUser) {
    // For testing purposes, we might need to set up a mock authentication state
    // This could involve setting localStorage, sessionStorage, or cookies
    
    await this.page.goto('/');
    
    // Set mock authentication data in localStorage
    await this.page.evaluate((userData) => {
      localStorage.setItem('msal.account.keys', JSON.stringify(['test-account-key']));
      localStorage.setItem('msal.account.test-account-key', JSON.stringify({
        homeAccountId: 'test-home-account-id',
        environment: 'login.microsoftonline.com',
        tenantId: 'test-tenant-id',
        username: userData.email,
        localAccountId: 'test-local-account-id',
        name: userData.displayName || userData.email,
        idTokenClaims: {
          aud: 'test-client-id',
          iss: 'https://login.microsoftonline.com/test-tenant-id/v2.0',
          sub: 'test-subject-id',
          email: userData.email,
          name: userData.displayName || userData.email,
          roles: [userData.role]
        }
      }));
      
      localStorage.setItem('msal.token.keys.test-client-id', JSON.stringify(['test-token-key']));
      localStorage.setItem('msal.token.test-token-key', JSON.stringify({
        credentialType: 'AccessToken',
        homeAccountId: 'test-home-account-id',
        environment: 'login.microsoftonline.com',
        clientId: 'test-client-id',
        secret: 'test-access-token',
        tokenType: 'Bearer',
        expiresOn: Math.floor(Date.now() / 1000) + 3600 // 1 hour from now
      }));
    }, user);
    
    // Reload the page to apply the authentication state
    await this.page.reload();
    await waitForAppLoad(this.page);
  }

  /**
   * Check if user is logged in
   */
  async isLoggedIn(): Promise<boolean> {
    try {
      // Look for indicators that the user is logged in
      const loggedInIndicators = [
        '.ant-layout-header', // Header should be visible when logged in
        '[data-testid="user-menu"]', // User menu
        '.ant-menu', // Navigation menu
        'button:has-text("Logout")', // Logout button
      ];

      for (const indicator of loggedInIndicators) {
        if (await this.page.locator(indicator).isVisible({ timeout: 2000 })) {
          return true;
        }
      }

      // Check localStorage for authentication tokens
      const hasAuthTokens = await this.page.evaluate(() => {
        const msalKeys = localStorage.getItem('msal.account.keys');
        return msalKeys && JSON.parse(msalKeys).length > 0;
      });

      return hasAuthTokens;
    } catch {
      return false;
    }
  }

  /**
   * Logout user
   */
  async logout() {
    try {
      // Look for logout button
      const logoutButton = this.page.locator('button:has-text("Logout"), .ant-btn:has-text("Sign out")');
      
      if (await logoutButton.isVisible({ timeout: 5000 })) {
        await logoutButton.click();
        await this.page.waitForLoadState('networkidle');
      } else {
        // Clear authentication data manually
        await this.page.evaluate(() => {
          localStorage.clear();
          sessionStorage.clear();
        });
        await this.page.reload();
      }
      
      await waitForAppLoad(this.page);
    } catch (error) {
      console.warn('Logout failed:', error);
    }
  }

  /**
   * Get current user info
   */
  async getCurrentUser(): Promise<any> {
    try {
      return await this.page.evaluate(() => {
        const accountKeys = localStorage.getItem('msal.account.keys');
        if (!accountKeys) return null;
        
        const keys = JSON.parse(accountKeys);
        if (keys.length === 0) return null;
        
        const accountData = localStorage.getItem(`msal.account.${keys[0]}`);
        return accountData ? JSON.parse(accountData) : null;
      });
    } catch {
      return null;
    }
  }

  /**
   * Setup authentication for test context
   */
  static async setupAuthForContext(context: BrowserContext, user: TestUser) {
    // Add authentication headers or cookies if needed
    await context.addInitScript((userData) => {
      // This script runs before every page load
      window.localStorage.setItem('test-user', JSON.stringify(userData));
    }, user);
  }
}

/**
 * Create authentication helper
 */
export function createAuthHelper(page: Page, context: BrowserContext): AuthHelper {
  return new AuthHelper(page, context);
}

/**
 * Login fixture for tests
 */
export async function loginAsUser(page: Page, context: BrowserContext, userType: keyof typeof TEST_USERS = 'admin') {
  const user = TEST_USERS[userType];
  const authHelper = createAuthHelper(page, context);
  
  // Try MSAL login first, fall back to testing login
  try {
    await authHelper.loginWithMsal(user);
  } catch (error) {
    console.log('MSAL login failed, using testing login:', error);
    await authHelper.loginForTesting(user);
  }
  
  return authHelper;
}
