export interface PersonalInfo {
  full_name: string;
  email: string;
  phone_number?: string;
  country?: string;
  city?: string;
  address?: string;
  github?: string;
  linkedin_profile?: string;
  website?: string;
}

export interface WorkExperience {
  company_name?: string;
  job_title?: string;
  location?: string;
  start_date?: string;
  end_date?: string;
  responsibilities?: string[];
  skills?: { name: string }[];
}

export interface Education {
  institution_name?: string;
  degree?: string;
  location?: string;
  start_date?: string;
  end_date?: string;
}

export interface Certification {
  name?: string;
  skills?: string[];
  issue_date?: string;
}

export interface CandidateInfo {
  personal_info: PersonalInfo;
  work_experience: WorkExperience[];
  education: Education[];
  certifications: Certification[];
  skills: { name: string }[];
  soft_skills?: { name: string }[];
  languages: { language: string }[];
  summary?: string;
  roles?: string[];
}

export interface Candidate {
  id: string;
  candidate_id: string;
  candidate_info: CandidateInfo;
  proj_id: string;
  suggested_positions: any[];
  analysis_status: string;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
  created_by?: string;
  updated_by?: string;
  reason_info?: any;
  interview_data?: InterviewData;
  analysis_data?: AnalysisData;
  // HR Interview fields
  feedback_hr?: any;
  recruiter_hr_id?: string;
  scheduled_hr_id?: string;
  interview_date_hr?: string;
  feedback_date_hr?: string;
  status_hr?: string;
  recommendation_hr?: boolean;
  transcript_hr?: string;
  // Tech Interview fields
  feedback_tec?: any;
  recruiter_tec_id?: string;
  scheduled_tec_id?: string;
  interview_date_tec?: string;
  feedback_date_tec?: string;
  status_tec?: string;
  recommendation_tec?: boolean;
  transcript_tec?: string;
}

export interface InterviewData {
  percentage_of_match?: number;
  overall_seniority?: string;
  explanation?: string;
}

export interface AnalysisData {
  compatibilityPercentage?: number;
  recommendation?: boolean;
  justification?: string;
  matchesFound?: string[];
  missing?: string[];
}

export interface Note {
  id: string;
  candidate_id: string;
  notes: {
    note?: string;
    [key: string]: any;
  };
  created_at: string;
  updated_at: string;
  created_by: string;
  updated_by?: string;
} 