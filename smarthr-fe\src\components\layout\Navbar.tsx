import React from 'react';
import { Menu } from 'antd';
import { Link, useLocation } from 'react-router-dom';

export const Navbar: React.FC = () => {
  const location = useLocation();

  const items = [
    {
      key: '/',
      label: <Link to='/'>Open Positions</Link>
    },
    {
      key: '/candidates',
      label: <Link to='/candidates'>Candidates</Link>
    }
  ];

  return (
    <Menu
      mode='horizontal'
      selectedKeys={[location.pathname]}
      items={items}
      style={{
        border: 'none',
        background: 'transparent',
        fontWeight: 500,
        fontSize: 16,
        minWidth: 'auto'
      }}
    />
  );
};
