import { test, expect } from '../fixtures';
import { createAssertions } from '../utils/assertions';
import { CandidatesPage } from '../pages';

test.describe('Candidate Management', () => {
  test('should display candidates list', async ({ candidatesPage }) => {
    const assertions = createAssertions(candidatesPage.page);
    
    await candidatesPage.goto();
    
    // Should see the candidates table
    await expect(candidatesPage.candidatesTable).toBeVisible();
    
    // Should have at least the table structure even if empty
    await assertions.expectLoadingComplete();
    
    // Check if there are candidates or empty state
    const hasCandidates = await candidatesPage.candidateRows.count() > 0;
    const hasEmptyState = await candidatesPage.page.locator('.ant-empty').isVisible();
    
    expect(hasCandidates || hasEmptyState).toBeTruthy();
  });

  test('should search for candidates', async ({ candidatesPage, createdCandidateId }) => {
    const assertions = createAssertions(candidatesPage.page);
    
    await candidatesPage.goto();
    await assertions.expectLoadingComplete();
    
    // Get initial candidate count
    const initialCount = await candidatesPage.getCandidateCount();
    
    if (initialCount > 0) {
      // Get the first candidate name to search for
      const candidateNames = await candidatesPage.getCandidateNames();
      const firstCandidateName = candidateNames[0];
      
      // Search for the candidate
      await candidatesPage.searchForCandidate(firstCandidateName);
      
      // Should show filtered results
      await candidatesPage.expectCandidateToBeVisible(firstCandidateName);
      
      // Clear search
      await candidatesPage.clearSearch();
      
      // Should show all candidates again
      const finalCount = await candidatesPage.getCandidateCount();
      expect(finalCount).toBeGreaterThanOrEqual(initialCount);
    } else {
      console.log('No candidates available for search test');
    }
  });

  test('should filter candidates by role', async ({ candidatesPage }) => {
    const assertions = createAssertions(candidatesPage.page);
    
    await candidatesPage.goto();
    await assertions.expectLoadingComplete();
    
    const initialCount = await candidatesPage.getCandidateCount();
    
    if (initialCount > 0) {
      // Try filtering by role if filter exists
      const roleFilter = candidatesPage.roleFilter;
      if (await roleFilter.isVisible()) {
        // Open role filter dropdown
        await roleFilter.click();
        const roleOptions = candidatesPage.page.locator('.ant-select-item-option');
        const optionCount = await roleOptions.count();
        
        if (optionCount > 0) {
          const firstRole = await roleOptions.first().textContent();
          await roleOptions.first().click();
          
          // Wait for filtering to complete
          await assertions.expectLoadingComplete();
          
          // Should show filtered results
          const filteredCount = await candidatesPage.getCandidateCount();
          expect(filteredCount).toBeGreaterThanOrEqual(0);
          
          // Clear filter by selecting "All" or similar option
          await roleFilter.click();
          const allOption = candidatesPage.page.locator('.ant-select-item-option:has-text("All")');
          if (await allOption.isVisible()) {
            await allOption.click();
          } else {
            // Close dropdown and try clearing differently
            await candidatesPage.page.keyboard.press('Escape');
          }
        } else {
          // Close dropdown if no options
          await candidatesPage.page.keyboard.press('Escape');
        }
      }
    } else {
      console.log('No candidates available for filter test');
    }
  });

  test('should filter candidates by location', async ({ candidatesPage }) => {
    const assertions = createAssertions(candidatesPage.page);
    
    await candidatesPage.goto();
    await assertions.expectLoadingComplete();
    
    const initialCount = await candidatesPage.getCandidateCount();
    
    if (initialCount > 0) {
      // Try filtering by location if filter exists
      const locationFilter = candidatesPage.locationFilter;
      if (await locationFilter.isVisible()) {
        await locationFilter.click();
        const locationOptions = candidatesPage.page.locator('.ant-select-item-option');
        const optionCount = await locationOptions.count();
        
        if (optionCount > 0) {
          await locationOptions.first().click();
          await assertions.expectLoadingComplete();
          
          // Should show filtered results
          const filteredCount = await candidatesPage.getCandidateCount();
          expect(filteredCount).toBeGreaterThanOrEqual(0);
        } else {
          await candidatesPage.page.keyboard.press('Escape');
        }
      }
    } else {
      console.log('No candidates available for location filter test');
    }
  });

  test('should view candidate details', async ({ candidatesPage }) => {
    const assertions = createAssertions(candidatesPage.page);
    
    await candidatesPage.goto();
    await assertions.expectLoadingComplete();
    
    const candidateCount = await candidatesPage.getCandidateCount();
    
    if (candidateCount > 0) {
      const candidateNames = await candidatesPage.getCandidateNames();
      const firstCandidateName = candidateNames[0];
      
      // Try to view candidate details
      try {
        await candidatesPage.viewCandidateDetails(firstCandidateName);
        
        // Should open modal, drawer, or navigate to details page
        const candidateDetails = candidatesPage.page.locator('.ant-modal, .ant-drawer, [data-testid="candidate-details"]');
        await expect(candidateDetails).toBeVisible();
        
        // Close modal/drawer if it opened
        const closeButton = candidateDetails.locator('.ant-modal-close, .ant-drawer-close, button:has-text("Close")');
        if (await closeButton.isVisible()) {
          await closeButton.click();
        }
      } catch (error) {
        console.log('View details not available or different interaction pattern');
      }
    } else {
      console.log('No candidates available for details view test');
    }
  });

  test('should handle candidate pagination', async ({ candidatesPage }) => {
    const assertions = createAssertions(candidatesPage.page);
    
    await candidatesPage.goto();
    await assertions.expectLoadingComplete();
    
    // Check if pagination exists
    const pagination = candidatesPage.page.locator('.ant-pagination');
    
    if (await pagination.isVisible()) {
      const nextButton = pagination.locator('.ant-pagination-next');
      const isNextEnabled = await nextButton.isEnabled();
      
      if (isNextEnabled) {
        const currentPage = await candidatesPage.getCurrentPage();
        
        // Go to next page
        await candidatesPage.goToNextPage();
        
        // Should be on next page
        const newPage = await candidatesPage.getCurrentPage();
        expect(newPage).toBe(currentPage + 1);
        
        // Go back to previous page
        await candidatesPage.goToPreviousPage();
        
        // Should be back on original page
        const finalPage = await candidatesPage.getCurrentPage();
        expect(finalPage).toBe(currentPage);
      }
    } else {
      console.log('No pagination available - all candidates fit on one page');
    }
  });

  test('should display candidate information correctly', async ({ candidatesPage, createdCandidateId }) => {
    const assertions = createAssertions(candidatesPage.page);
    
    await candidatesPage.goto();
    await assertions.expectLoadingComplete();
    
    const candidateCount = await candidatesPage.getCandidateCount();
    
    if (candidateCount > 0) {
      const candidateNames = await candidatesPage.getCandidateNames();
      const firstCandidateName = candidateNames[0];
      
      // Get candidate details from the table
      const candidateDetails = await candidatesPage.getCandidateDetails(firstCandidateName);
      
      // Should have candidate information
      expect(Object.keys(candidateDetails).length).toBeGreaterThan(0);
      
      // Candidate name should not be empty
      expect(candidateDetails.column_0 || firstCandidateName).toBeTruthy();
    } else {
      console.log('No candidates available for information display test');
    }
  });

  test('should handle empty candidates list', async ({ candidatesPage }) => {
    const assertions = createAssertions(candidatesPage.page);
    
    await candidatesPage.goto();
    await assertions.expectLoadingComplete();
    
    // Search for something that doesn't exist
    await candidatesPage.searchForCandidate('NonExistentCandidateXYZ123');
    
    // Should show empty state or no results
    const hasEmptyState = await candidatesPage.page.locator('.ant-empty, .ant-table-placeholder').isVisible();
    const hasNoRows = await candidatesPage.candidateRows.count() === 0;
    
    expect(hasEmptyState || hasNoRows).toBeTruthy();
    
    // Clear search to restore normal state
    await candidatesPage.clearSearch();
  });

  test('should add new candidate', async ({ candidatesPage }) => {
    const assertions = createAssertions(candidatesPage.page);
    
    await candidatesPage.goto();
    await assertions.expectLoadingComplete();
    
    // Look for add candidate button
    const addButton = candidatesPage.addCandidateButton;
    
    if (await addButton.isVisible()) {
      await candidatesPage.clickAddCandidate();
      
      // Should open add candidate modal/drawer/form
      const addCandidateForm = candidatesPage.page.locator('.ant-modal, .ant-drawer, [data-testid="add-candidate-form"]');
      await expect(addCandidateForm).toBeVisible();
      
      // Close the form
      const closeButton = addCandidateForm.locator('.ant-modal-close, .ant-drawer-close, button:has-text("Cancel")');
      if (await closeButton.isVisible()) {
        await closeButton.click();
      } else {
        // Try pressing Escape
        await candidatesPage.page.keyboard.press('Escape');
      }
      
      // Form should be closed
      await expect(addCandidateForm).not.toBeVisible();
    } else {
      console.log('Add candidate button not available');
    }
  });

  test('should handle candidate actions', async ({ candidatesPage }) => {
    const assertions = createAssertions(candidatesPage.page);
    
    await candidatesPage.goto();
    await assertions.expectLoadingComplete();
    
    const candidateCount = await candidatesPage.getCandidateCount();
    
    if (candidateCount > 0) {
      const candidateNames = await candidatesPage.getCandidateNames();
      const firstCandidateName = candidateNames[0];
      const candidateRow = candidatesPage.getCandidateRowByName(firstCandidateName);
      
      // Look for action buttons in the row
      const actionButtons = candidateRow.locator('.ant-btn, button');
      const buttonCount = await actionButtons.count();
      
      if (buttonCount > 0) {
        // Check what actions are available
        for (let i = 0; i < Math.min(buttonCount, 3); i++) {
          const button = actionButtons.nth(i);
          const buttonText = await button.textContent();
          console.log(`Available action: ${buttonText}`);
        }
      } else {
        console.log('No action buttons found in candidate rows');
      }
    } else {
      console.log('No candidates available for actions test');
    }
  });

  test('should refresh candidates list', async ({ candidatesPage }) => {
    const assertions = createAssertions(candidatesPage.page);
    
    await candidatesPage.goto();
    await assertions.expectLoadingComplete();
    
    const initialCount = await candidatesPage.getCandidateCount();
    
    // Refresh the page
    await candidatesPage.page.reload();
    await assertions.expectLoadingComplete();
    
    // Should still show candidates
    const finalCount = await candidatesPage.getCandidateCount();
    expect(finalCount).toBe(initialCount);
  });

  test('should handle candidate export', async ({ candidatesPage }) => {
    const assertions = createAssertions(candidatesPage.page);
    
    await candidatesPage.goto();
    await assertions.expectLoadingComplete();
    
    // Look for export button
    const exportButton = candidatesPage.exportButton;
    
    if (await exportButton.isVisible()) {
      // Test export functionality
      const downloadPromise = candidatesPage.page.waitForEvent('download');
      await exportButton.click();
      
      try {
        const download = await downloadPromise;
        expect(download.suggestedFilename()).toBeTruthy();
        console.log(`Export file: ${download.suggestedFilename()}`);
      } catch (error) {
        console.log('Export may require additional steps or different interaction');
      }
    } else {
      console.log('Export button not available');
    }
  });
});
