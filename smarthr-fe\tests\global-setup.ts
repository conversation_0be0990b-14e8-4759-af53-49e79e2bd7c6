import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global setup for SmartHR E2E tests...');
  
  // Launch browser for setup
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // Wait for the application to be ready
    console.log('⏳ Waiting for application to be ready...');
    await page.goto(config.projects[0].use.baseURL || 'http://localhost:5173');
    
    // Wait for the main app to load
    await page.waitForSelector('[data-testid="app-container"], .App', { timeout: 30000 });
    
    console.log('✅ Application is ready for testing');
    
    // You can add more global setup here like:
    // - Database seeding
    // - Authentication token generation
    // - Test data preparation
    
  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  } finally {
    await context.close();
    await browser.close();
  }
  
  console.log('✅ Global setup completed successfully');
}

export default globalSetup;
