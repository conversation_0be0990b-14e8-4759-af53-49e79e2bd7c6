// Candidate List Style Constants

export const COLORS = {
  PRIMARY: '#6366F1',
  PROGRESS_GREEN: '#52C41A',
  PROGRESS_BLUE: '#2F54EB',
  BACKGROUND_BLUE: '#E6F7FF',
  BORDER_GRAY: '#F1F1F1',
  TEXT_PRIMARY: '#000000',
  TEXT_SECONDARY: '#666666',
  TEXT_GRAY: '#262626',
  COUNT_BADGE: '#08979C',
  ACTIVE_PILL: '#E6EFFF',
  ACTIVE_PILL_TEXT: '#6C52FF',
  ICON_BLUE: '#69C0FF'
} as const;

export const SIZES = {
  PROGRESS_LARGE: 44,
  PROGRESS_MEDIUM: 42,
  AVATAR_LARGE: 64,
  AVATAR_MEDIUM: 32,
  COUNT_BADGE: 28
} as const;

export const CANDIDATE_CARD_STYLES = {
  container: {
    width: '100%',
    marginBottom: 16,
    cursor: 'pointer',
    borderRadius: 10,
    border: `1px solid ${COLORS.BORDER_GRAY}`,
    boxShadow: '0px 4px 4px 0px #00000026',
    backgroundColor: '#FFFFFF'
  },
  header: {
    display: 'flex',
    alignItems: 'center',
    gap: 8,
    marginBottom: 12
  },
  avatar: {
    backgroundColor: COLORS.PRIMARY,
    width: SIZES.AVATAR_MEDIUM,
    height: SIZES.AVATAR_MEDIUM,
    fontSize: 14
  },
  name: {
    fontWeight: 500,
    fontSize: 16,
    color: COLORS.TEXT_PRIMARY
  },
  separator: {
    height: 1,
    backgroundColor: COLORS.BORDER_GRAY,
    margin: '12px 0'
  },
  progressContainer: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 8
  },
  progressSection: {
    display: 'flex',
    alignItems: 'center',
    gap: 8
  },
  progressLabel: {
    fontSize: 12,
    color: COLORS.TEXT_SECONDARY,
    fontWeight: 500
  }
} as const;

export const ANALYSIS_SECTION_STYLES = {
  container: {
    background: COLORS.BACKGROUND_BLUE,
    borderRadius: 8,
    padding: '8px 8px 8px 16px',
    marginBottom: 24
  },
  progressContainer: {
    display: 'flex',
    alignItems: 'center',
    gap: 16
  },
  progressColumn: {
    display: 'flex',
    flexDirection: 'column' as const,
    alignItems: 'center',
    gap: 6
  },
  progressLabel: {
    fontSize: 12,
    fontWeight: 700,
    color: '#141414',
    textAlign: 'center' as const
  },
  feedbackSection: {
    borderLeft: `1px solid ${COLORS.PROGRESS_BLUE}`,
    paddingLeft: 24,
    paddingTop: 8,
    paddingBottom: 8,
    minHeight: '96px',
    display: 'flex',
    flexDirection: 'column' as const,
    justifyContent: 'center'
  },
  feedbackTitle: {
    fontSize: 14,
    fontWeight: 600,
    color: COLORS.TEXT_PRIMARY,
    marginBottom: 8
  },
  feedbackText: {
    fontSize: 12,
    lineHeight: '20px',
    color: COLORS.TEXT_GRAY
  }
} as const;

export const NAVIGATION_PILL_STYLES = {
  container: {
    display: 'flex',
    gap: 8,
    marginBottom: 24,
    borderBottom: '1px solid #EAECF0',
    paddingBottom: 16
  },
  pill: (isActive: boolean) => ({
    padding: '8px 16px',
    borderRadius: 50,
    cursor: 'pointer',
    background: isActive ? COLORS.ACTIVE_PILL : 'transparent',
    color: isActive ? COLORS.ACTIVE_PILL_TEXT : 'rgba(0, 0, 0, 0.85)',
    fontSize: 14,
    fontWeight: 400,
    display: 'flex',
    alignItems: 'center',
    gap: 8,
    transition: 'all 0.2s'
  }),
  icon: (isActive: boolean) => ({
    fontSize: 15,
    color: isActive ? COLORS.ACTIVE_PILL_TEXT : COLORS.ICON_BLUE
  })
} as const;

export const HEADER_STYLES = {
  container: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16
  },
  title: {
    margin: 0,
    fontSize: 18,
    fontWeight: 600,
    color: COLORS.TEXT_PRIMARY
  },
  countBadge: {
    background: COLORS.PRIMARY,
    color: 'white',
    width: SIZES.COUNT_BADGE,
    height: SIZES.COUNT_BADGE,
    borderRadius: 14,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: 14,
    fontWeight: 'bold'
  }
} as const;