import { useState } from 'react';
import { message } from 'antd';
import type { Dayjs } from 'dayjs';
import { api } from '../utils/api';
import { Candidate } from '../types/candidate';
import { createHRFeedbackPayload, createTechFeedbackPayload } from '../utils/candidateUtils';

interface InterviewFormState {
  recruiter: string;
  interviewDate: Dayjs | null;
  scheduledBy: string;
  feedbackDate: Dayjs | null;
  status: string | undefined;
  recommendation: string | undefined;
  comments: string;
  transcript: string;
}

export const useInterviewFeedback = (positionId: string) => {
  // HR Form State
  const [hrState, setHrState] = useState<InterviewFormState>({
    recruiter: '',
    interviewDate: null,
    scheduledBy: '',
    feedbackDate: null,
    status: undefined,
    recommendation: undefined,
    comments: '',
    transcript: ''
  });

  // Tech Form State
  const [techState, setTechState] = useState<InterviewFormState>({
    recruiter: '',
    interviewDate: null,
    scheduledBy: '',
    feedbackDate: null,
    status: undefined,
    recommendation: undefined,
    comments: '',
    transcript: ''
  });

  const resetHRForm = () => {
    setHrState({
      recruiter: '',
      interviewDate: null,
      scheduledBy: '',
      feedbackDate: null,
      status: undefined,
      recommendation: undefined,
      comments: '',
      transcript: ''
    });
  };

  const resetTechForm = () => {
    setTechState({
      recruiter: '',
      interviewDate: null,
      scheduledBy: '',
      feedbackDate: null,
      status: undefined,
      recommendation: undefined,
      comments: '',
      transcript: ''
    });
  };

  const submitHRFeedback = async (
    candidate: Candidate,
    values: any,
    onSuccess?: (updatedCandidate: Candidate) => void
  ) => {
    try {
      const payload = createHRFeedbackPayload(positionId, candidate, values);
      const response = await api.put('/interview/hr', payload);
      
      // Create updated candidate object with the new feedback data
      // Automatically set status to 'completed' when feedback is saved
      const updatedCandidate: Candidate = {
        ...candidate,
        recruiter_hr_id: values.hrRecruiter,
        scheduled_hr_id: values.hrScheduledBy,
        feedback_hr: { comments: values.hrComments },
        interview_date_hr: values.hrInterviewDate ? values.hrInterviewDate.toISOString() : null,
        feedback_date_hr: values.hrFeedbackDate ? values.hrFeedbackDate.toISOString() : new Date().toISOString(),
        status_hr: values.hrStatus, // 'completed', // Always set to completed when feedback is saved
        recommendation_hr: values.hrRecommendation === 'I recommend continuing the process',
        transcript_hr: values.hrTranscript || ''
      };

      message.success('HR Feedback saved successfully!');
      onSuccess?.(updatedCandidate);
    } catch (error) {
      console.error('Failed to submit HR feedback:', error);
      message.error(`Failed to save HR feedback. Please try again.`);
    }
  };

  const submitTechFeedback = async (
    candidate: Candidate,
    values: any,
    onSuccess?: (updatedCandidate: Candidate) => void
  ) => {
    try {
      const payload = createTechFeedbackPayload(positionId, candidate, values);
      const response = await api.put('/interview/tec', payload);
      
      // Create updated candidate object with the new feedback data
      // Automatically set status to 'completed' when feedback is saved
      const updatedCandidate: Candidate = {
        ...candidate,
        recruiter_tec_id: values.techRecruiter,
        scheduled_tec_id: values.techScheduledBy,
        feedback_tec: { additionalProp1: values.techComments },
        interview_date_tec: values.techInterviewDate ? values.techInterviewDate.toISOString() : null,
        feedback_date_tec: values.techFeedbackDate ? values.techFeedbackDate.toISOString() : new Date().toISOString(),
        status_tec: values.techStatus, // 'completed', // Always set to completed when feedback is saved
        recommendation_tec: values.techRecommendation === 'I recommend continuing the process',
        transcript_tec: values.techTranscript || ''
      };

      message.success('Technical Feedback saved successfully!');
      onSuccess?.(updatedCandidate);
    } catch (error) {
      console.error('Failed to submit technical feedback:', error);
      message.error(`Failed to save technical feedback. Please try again.`);
    }
  };

  return {
    hrState,
    techState,
    setHrState,
    setTechState,
    resetHRForm,
    resetTechForm,
    submitHRFeedback,
    submitTechFeedback
  };
};

export default useInterviewFeedback;