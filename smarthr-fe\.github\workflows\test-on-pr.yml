name: PR Tests

on:
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'smarthr-fe/**'
      - 'smarthr-be/**'
      - '.github/workflows/**'

env:
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.11'

jobs:
  # Quick smoke tests for PRs
  smoke-tests:
    name: Smoke Tests
    runs-on: ubuntu-latest
    timeout-minutes: 20
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: smarthr-fe/package-lock.json
          
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          
      - name: Install frontend dependencies
        working-directory: smarthr-fe
        run: npm ci
        
      - name: Install backend dependencies
        working-directory: smarthr-be
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          
      - name: Install Playwright browsers
        working-directory: smarthr-fe
        run: npx playwright install --with-deps chromium
        
      - name: Setup PostgreSQL
        uses: harmon758/postgresql-action@v1
        with:
          postgresql version: '15'
          postgresql db: smarthr_test
          postgresql user: postgres
          postgresql password: password
          
      - name: Initialize test database
        working-directory: smarthr-fe
        run: |
          PGPASSWORD=password psql -h localhost -U postgres -d smarthr_test -f tests/fixtures/test-db-init.sql
          
      - name: Start backend server
        working-directory: smarthr-be
        run: |
          export DATABASE_URL="postgresql://postgres:password@localhost:5432/smarthr_test"
          export JWT_SECRET="test-secret-key"
          export ENVIRONMENT="test"
          python -m uvicorn main:app --host 0.0.0.0 --port 8080 &
          sleep 10
          
      - name: Start frontend server
        working-directory: smarthr-fe
        run: |
          export VITE_API_BASE_URL="http://localhost:8080"
          npm run dev &
          sleep 15
          
      - name: Wait for services
        run: |
          timeout 60 bash -c 'until curl -f http://localhost:8080/health 2>/dev/null; do sleep 2; done'
          timeout 60 bash -c 'until curl -f http://localhost:5173 2>/dev/null; do sleep 2; done'
          
      - name: Run smoke tests
        working-directory: smarthr-fe
        run: |
          # Run critical path tests only
          npx playwright test --project=chromium --grep="should load the application|should navigate between main pages|should display.*list"
        env:
          CI: true
          
      - name: Upload smoke test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: smoke-test-results
          path: |
            smarthr-fe/test-results/
            smarthr-fe/playwright-report/
          retention-days: 7

  # Check for test file changes
  test-changes:
    name: Check Test Changes
    runs-on: ubuntu-latest
    outputs:
      tests-changed: ${{ steps.changes.outputs.tests }}
      
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          
      - name: Check for test changes
        id: changes
        run: |
          if git diff --name-only origin/main...HEAD | grep -E "(test|spec)" > /dev/null; then
            echo "tests=true" >> $GITHUB_OUTPUT
          else
            echo "tests=false" >> $GITHUB_OUTPUT
          fi

  # Run full tests if test files changed
  full-tests:
    name: Full E2E Tests
    runs-on: ubuntu-latest
    needs: test-changes
    if: needs.test-changes.outputs.tests-changed == 'true'
    timeout-minutes: 45
    
    strategy:
      fail-fast: false
      matrix:
        browser: [chromium, firefox]
        
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: smarthr-fe/package-lock.json
          
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          
      - name: Install frontend dependencies
        working-directory: smarthr-fe
        run: npm ci
        
      - name: Install backend dependencies
        working-directory: smarthr-be
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          
      - name: Install Playwright browsers
        working-directory: smarthr-fe
        run: npx playwright install --with-deps ${{ matrix.browser }}
        
      - name: Setup PostgreSQL
        uses: harmon758/postgresql-action@v1
        with:
          postgresql version: '15'
          postgresql db: smarthr_test
          postgresql user: postgres
          postgresql password: password
          
      - name: Initialize test database
        working-directory: smarthr-fe
        run: |
          PGPASSWORD=password psql -h localhost -U postgres -d smarthr_test -f tests/fixtures/test-db-init.sql
          
      - name: Start backend server
        working-directory: smarthr-be
        run: |
          export DATABASE_URL="postgresql://postgres:password@localhost:5432/smarthr_test"
          export JWT_SECRET="test-secret-key"
          export ENVIRONMENT="test"
          python -m uvicorn main:app --host 0.0.0.0 --port 8080 &
          sleep 10
          
      - name: Start frontend server
        working-directory: smarthr-fe
        run: |
          export VITE_API_BASE_URL="http://localhost:8080"
          npm run dev &
          sleep 15
          
      - name: Wait for services
        run: |
          timeout 60 bash -c 'until curl -f http://localhost:8080/health 2>/dev/null; do sleep 2; done'
          timeout 60 bash -c 'until curl -f http://localhost:5173 2>/dev/null; do sleep 2; done'
          
      - name: Run full E2E tests
        working-directory: smarthr-fe
        run: npx playwright test --project=${{ matrix.browser }}
        env:
          CI: true
          
      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: full-test-results-${{ matrix.browser }}
          path: |
            smarthr-fe/test-results/
            smarthr-fe/playwright-report/
          retention-days: 14

  # Comment on PR with results
  comment-results:
    name: Comment Test Results
    runs-on: ubuntu-latest
    needs: [smoke-tests, full-tests]
    if: always()
    
    steps:
      - name: Download artifacts
        uses: actions/download-artifact@v4
        
      - name: Comment PR with results
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const path = require('path');
            
            let comment = '## 🧪 PR Test Results\n\n';
            
            // Check smoke test results
            const smokeTestPassed = '${{ needs.smoke-tests.result }}' === 'success';
            comment += `### Smoke Tests: ${smokeTestPassed ? '✅ PASSED' : '❌ FAILED'}\n`;
            
            if ('${{ needs.test-changes.outputs.tests-changed }}' === 'true') {
              const fullTestPassed = '${{ needs.full-tests.result }}' === 'success';
              comment += `### Full E2E Tests: ${fullTestPassed ? '✅ PASSED' : '❌ FAILED'}\n`;
              comment += '_Full tests ran because test files were modified._\n';
            } else {
              comment += '### Full E2E Tests: ⏭️ SKIPPED\n';
              comment += '_Full tests skipped - no test file changes detected._\n';
            }
            
            comment += '\n---\n';
            comment += '_This comment will be updated when tests complete._';
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
