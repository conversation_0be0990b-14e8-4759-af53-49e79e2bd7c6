import { test, expect } from '../fixtures';
import { createAssertions } from '../utils/assertions';

test.describe('Navigation', () => {
  test('should navigate between main pages', async ({ loggedInPage }) => {
    const assertions = createAssertions(loggedInPage);
    
    // Start at home page
    await loggedInPage.goto('/');
    await assertions.expectLoadingComplete();
    
    // Should be on job orders page
    await assertions.expectUrlToMatch(/\/$/);
    
    // Navigate to candidates page
    const candidatesLink = loggedInPage.locator('a[href="/candidates"], .ant-menu-item:has-text("Candidates")');
    if (await candidatesLink.isVisible()) {
      await candidatesLink.click();
      await assertions.expectLoadingComplete();
      await assertions.expectUrlToMatch(/\/candidates/);
      
      // Should see candidates table
      await expect(loggedInPage.locator('.ant-table, [data-testid="candidates-table"]')).toBeVisible();
    }
    
    // Navigate back to jobs
    const jobsLink = loggedInPage.locator('a[href="/"], .ant-menu-item:has-text("Open Positions"), .ant-menu-item:has-text("Jobs")');
    if (await jobsLink.isVisible()) {
      await jobsLink.click();
      await assertions.expectLoadingComplete();
      await assertions.expectUrlToMatch(/\/$/);
      
      // Should see jobs table
      await expect(loggedInPage.locator('.ant-table, [data-testid="job-orders-table"]')).toBeVisible();
    }
  });

  test('should handle direct URL navigation', async ({ loggedInPage }) => {
    const assertions = createAssertions(loggedInPage);
    
    // Navigate directly to candidates page
    await loggedInPage.goto('/candidates');
    await assertions.expectLoadingComplete();
    
    // Should load candidates page correctly
    await expect(loggedInPage.locator('.ant-table, [data-testid="candidates-table"]')).toBeVisible();
    await assertions.expectUrlToMatch(/\/candidates/);
    
    // Navigate directly to home page
    await loggedInPage.goto('/');
    await assertions.expectLoadingComplete();
    
    // Should load home page correctly
    await expect(loggedInPage.locator('.ant-table, [data-testid="job-orders-table"]')).toBeVisible();
    await assertions.expectUrlToMatch(/\/$/);
  });

  test('should handle browser back and forward buttons', async ({ loggedInPage }) => {
    const assertions = createAssertions(loggedInPage);
    
    // Start at home page
    await loggedInPage.goto('/');
    await assertions.expectLoadingComplete();
    
    // Navigate to candidates
    await loggedInPage.goto('/candidates');
    await assertions.expectLoadingComplete();
    await assertions.expectUrlToMatch(/\/candidates/);
    
    // Use browser back button
    await loggedInPage.goBack();
    await assertions.expectLoadingComplete();
    await assertions.expectUrlToMatch(/\/$/);
    
    // Use browser forward button
    await loggedInPage.goForward();
    await assertions.expectLoadingComplete();
    await assertions.expectUrlToMatch(/\/candidates/);
  });

  test('should maintain active navigation state', async ({ loggedInPage }) => {
    const assertions = createAssertions(loggedInPage);
    
    // Navigate to home page
    await loggedInPage.goto('/');
    await assertions.expectLoadingComplete();
    
    // Check if navigation menu exists and shows active state
    const navMenu = loggedInPage.locator('.ant-menu');
    if (await navMenu.isVisible()) {
      // Home/Jobs should be active
      const activeItem = navMenu.locator('.ant-menu-item-selected, .ant-menu-item-active');
      await expect(activeItem).toBeVisible();
    }
    
    // Navigate to candidates
    await loggedInPage.goto('/candidates');
    await assertions.expectLoadingComplete();
    
    if (await navMenu.isVisible()) {
      // Candidates should now be active
      const candidatesItem = navMenu.locator('.ant-menu-item:has-text("Candidates")');
      if (await candidatesItem.isVisible()) {
        await expect(candidatesItem).toHaveClass(/ant-menu-item-selected|ant-menu-item-active/);
      }
    }
  });

  test('should handle job details navigation', async ({ loggedInPage, createdPositionId }) => {
    const assertions = createAssertions(loggedInPage);
    
    // Navigate to home page
    await loggedInPage.goto('/');
    await assertions.expectLoadingComplete();
    
    // Look for job rows in the table
    const jobTable = loggedInPage.locator('.ant-table-tbody');
    await expect(jobTable).toBeVisible();
    
    const jobRows = loggedInPage.locator('.ant-table-tbody tr');
    const rowCount = await jobRows.count();
    
    if (rowCount > 0) {
      // Click on the first job row
      await jobRows.first().click();
      await assertions.expectLoadingComplete();
      
      // Should navigate to job details page
      await assertions.expectUrlToMatch(/\/job\/[^\/]+/);
      
      // Should see job details content
      const jobDetailsContent = loggedInPage.locator('.ant-card, [data-testid="job-details"], .ant-tabs');
      await expect(jobDetailsContent).toBeVisible();
    } else {
      console.log('No jobs available for navigation test');
    }
  });

  test('should handle candidate details navigation', async ({ loggedInPage, createdCandidateId }) => {
    const assertions = createAssertions(loggedInPage);
    
    // Navigate to candidates page
    await loggedInPage.goto('/candidates');
    await assertions.expectLoadingComplete();
    
    // Look for candidate rows in the table
    const candidateTable = loggedInPage.locator('.ant-table-tbody');
    await expect(candidateTable).toBeVisible();
    
    const candidateRows = loggedInPage.locator('.ant-table-tbody tr');
    const rowCount = await candidateRows.count();
    
    if (rowCount > 0) {
      // Look for a view/details button or clickable row
      const firstRow = candidateRows.first();
      const viewButton = firstRow.locator('.ant-btn:has-text("View"), button:has-text("View")');
      
      if (await viewButton.isVisible()) {
        await viewButton.click();
      } else {
        // Try clicking the row itself
        await firstRow.click();
      }
      
      await assertions.expectLoadingComplete();
      
      // Should navigate to candidate details (could be modal, drawer, or new page)
      const candidateDetails = loggedInPage.locator('.ant-modal, .ant-drawer, [data-testid="candidate-details"]');
      await expect(candidateDetails).toBeVisible();
    } else {
      console.log('No candidates available for navigation test');
    }
  });

  test('should handle 404 errors gracefully', async ({ loggedInPage }) => {
    const assertions = createAssertions(loggedInPage);
    
    // Navigate to a non-existent route
    await loggedInPage.goto('/non-existent-page');
    await assertions.expectLoadingComplete();
    
    // Should either show 404 page or redirect to home
    const has404 = await loggedInPage.locator('.ant-result-404, .not-found, h1:has-text("404")').isVisible();
    const redirectedHome = loggedInPage.url().endsWith('/');
    
    expect(has404 || redirectedHome).toBeTruthy();
  });

  test('should handle deep linking to job details', async ({ loggedInPage, createdPositionId }) => {
    const assertions = createAssertions(loggedInPage);
    
    // Navigate directly to job details page
    await loggedInPage.goto(`/job/${createdPositionId}`);
    await assertions.expectLoadingComplete();
    
    // Should load job details page
    await assertions.expectUrlToMatch(new RegExp(`/job/${createdPositionId}`));
    
    // Should see job details content
    const jobDetailsContent = loggedInPage.locator('.ant-card, [data-testid="job-details"], .ant-tabs');
    await expect(jobDetailsContent).toBeVisible();
  });

  test('should handle navigation with query parameters', async ({ loggedInPage }) => {
    const assertions = createAssertions(loggedInPage);
    
    // Navigate with search query
    await loggedInPage.goto('/?search=engineer');
    await assertions.expectLoadingComplete();
    
    // Should preserve query parameters
    await assertions.expectUrlToMatch(/\?.*search=engineer/);
    
    // Navigate to candidates with filters
    await loggedInPage.goto('/candidates?role=developer');
    await assertions.expectLoadingComplete();
    
    // Should preserve query parameters
    await assertions.expectUrlToMatch(/\/candidates\?.*role=developer/);
  });

  test('should handle navigation during loading states', async ({ loggedInPage }) => {
    const assertions = createAssertions(loggedInPage);
    
    // Start navigation to a page
    const navigationPromise = loggedInPage.goto('/candidates');
    
    // Immediately try to navigate elsewhere
    await loggedInPage.goto('/');
    
    // Wait for navigation to complete
    await assertions.expectLoadingComplete();
    
    // Should end up at the final destination
    await assertions.expectUrlToMatch(/\/$/);
    
    // Page should be functional
    await expect(loggedInPage.locator('.ant-table, [data-testid="job-orders-table"]')).toBeVisible();
  });
});

test.describe('Navigation Edge Cases', () => {
  test('should handle rapid navigation clicks', async ({ loggedInPage }) => {
    const assertions = createAssertions(loggedInPage);
    
    await loggedInPage.goto('/');
    await assertions.expectLoadingComplete();
    
    // Rapidly click navigation links
    const candidatesLink = loggedInPage.locator('a[href="/candidates"], .ant-menu-item:has-text("Candidates")');
    const homeLink = loggedInPage.locator('a[href="/"], .ant-menu-item:has-text("Open Positions")');
    
    if (await candidatesLink.isVisible() && await homeLink.isVisible()) {
      // Click multiple times rapidly
      await candidatesLink.click();
      await homeLink.click();
      await candidatesLink.click();
      
      // Wait for final navigation to settle
      await assertions.expectLoadingComplete();
      
      // Should end up in a valid state
      const hasValidContent = await loggedInPage.locator('.ant-table, .App').isVisible();
      expect(hasValidContent).toBeTruthy();
    }
  });

  test('should handle navigation with network errors', async ({ loggedInPage }) => {
    const assertions = createAssertions(loggedInPage);
    
    // Start at home page
    await loggedInPage.goto('/');
    await assertions.expectLoadingComplete();
    
    // Simulate network error for API calls
    await loggedInPage.route('**/api/**', route => route.abort());
    
    // Try to navigate to candidates page
    await loggedInPage.goto('/candidates');
    
    // Should handle the error gracefully
    await loggedInPage.waitForLoadState('networkidle');
    
    // Should show error state or empty state, not crash
    const hasErrorHandling = await loggedInPage.locator('.ant-empty, .ant-result, .error-boundary, .ant-table').isVisible();
    expect(hasErrorHandling).toBeTruthy();
  });
});
