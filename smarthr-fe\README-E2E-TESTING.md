# SmartHR E2E Testing Guide

This document provides comprehensive information about the End-to-End (E2E) testing setup for the SmartHR application using Playwright.

## 📋 Table of Contents

- [Overview](#overview)
- [Setup](#setup)
- [Running Tests](#running-tests)
- [Test Structure](#test-structure)
- [Writing Tests](#writing-tests)
- [CI/CD Integration](#cicd-integration)
- [Docker Support](#docker-support)
- [Troubleshooting](#troubleshooting)

## 🎯 Overview

The SmartHR E2E testing suite uses <PERSON><PERSON> to test the complete user workflows across the application. It covers:

- **Authentication flows** - Login/logout, user sessions
- **Navigation** - Page routing, menu navigation
- **Job Management** - Creating, viewing, filtering job positions
- **Candidate Management** - Managing candidate profiles and applications
- **Interview Workflows** - Creating interviews, generating questions, conducting interviews
- **Visual Regression** - UI consistency across browsers and devices

## 🚀 Setup

### Prerequisites

- Node.js 18+ 
- Python 3.11+
- PostgreSQL 15+
- Docker (optional, for containerized testing)

### Installation

1. **Install dependencies:**
   ```bash
   cd smarthr-fe
   npm install
   ```

2. **Install Playwright browsers:**
   ```bash
   npx playwright install
   ```

3. **Set up test database:**
   ```bash
   # Create test database
   createdb smarthr_test
   
   # Initialize with test data
   psql -d smarthr_test -f tests/fixtures/test-db-init.sql
   ```

4. **Configure environment:**
   ```bash
   # Copy example environment file
   cp .env.example .env.test
   
   # Update with test configuration
   DATABASE_URL=postgresql://postgres:password@localhost:5432/smarthr_test
   JWT_SECRET=test-secret-key
   ```

## 🏃‍♂️ Running Tests

### Basic Commands

```bash
# Run all tests
npm run test:e2e

# Run tests in headed mode (with browser UI)
npm run test:e2e:headed

# Run tests in debug mode
npm run test:e2e:debug

# Run specific browser tests
npm run test:e2e:chromium
npm run test:e2e:firefox
npm run test:e2e:webkit

# Run mobile tests
npm run test:e2e:mobile

# Run visual regression tests
npm run test:visual

# Update visual regression snapshots
npm run test:visual:update
```

### Advanced Options

```bash
# Run specific test file
npx playwright test auth.spec.ts

# Run tests matching pattern
npx playwright test --grep "authentication"

# Run tests in parallel
npm run test:parallel

# Run tests serially
npm run test:serial

# Generate and view HTML report
npm run test:e2e:report
```

### Docker Testing

```bash
# Run tests in Docker
npm run test:docker

# Run tests in Docker with browser UI
npm run test:docker:headed

# Run tests in Docker debug mode
npm run test:docker:debug
```

## 📁 Test Structure

```
tests/
├── e2e/                          # E2E test files
│   ├── auth.spec.ts             # Authentication tests
│   ├── navigation.spec.ts       # Navigation tests
│   ├── job-management.spec.ts   # Job management tests
│   ├── candidate-management.spec.ts # Candidate tests
│   ├── interview-management.spec.ts # Interview tests
│   └── interview-conduct.spec.ts    # Interview conduct tests
├── visual/                       # Visual regression tests
│   └── visual-regression.spec.ts
├── fixtures/                     # Test fixtures and data
│   ├── index.ts                 # Test fixtures
│   ├── test-data.ts            # Test data generators
│   └── test-db-init.sql        # Database initialization
├── pages/                        # Page Object Models
│   ├── BasePage.ts              # Base page class
│   ├── JobOrdersPage.ts         # Job orders page
│   ├── JobDetailsPage.ts        # Job details page
│   ├── CandidatesPage.ts        # Candidates page
│   └── index.ts                 # Page exports
├── utils/                        # Test utilities
│   ├── test-helpers.ts          # Common test helpers
│   ├── auth.ts                  # Authentication utilities
│   ├── database.ts              # Database utilities
│   ├── assertions.ts            # Custom assertions
│   ├── test-reporter.ts         # Custom reporter
│   ├── performance-monitor.ts   # Performance monitoring
│   └── index.ts                 # Utility exports
├── global-setup.ts              # Global test setup
└── global-teardown.ts           # Global test teardown
```

## ✍️ Writing Tests

### Basic Test Structure

```typescript
import { test, expect } from '../fixtures';
import { createAssertions } from '../utils/assertions';

test.describe('Feature Name', () => {
  test('should perform specific action', async ({ loggedInPage }) => {
    const assertions = createAssertions(loggedInPage);
    
    // Navigate to page
    await loggedInPage.goto('/some-page');
    await assertions.expectLoadingComplete();
    
    // Perform actions
    await loggedInPage.click('button:has-text("Click Me")');
    
    // Assert results
    await assertions.expectSuccessNotification();
  });
});
```

### Using Page Objects

```typescript
import { test, expect } from '../fixtures';

test.describe('Job Management', () => {
  test('should search for jobs', async ({ jobOrdersPage }) => {
    await jobOrdersPage.goto();
    await jobOrdersPage.searchForJob('Software Engineer');
    await jobOrdersPage.expectJobToBeVisible('Software Engineer');
  });
});
```

### Using Test Fixtures

```typescript
import { test, expect } from '../fixtures';

test.describe('Interview Tests', () => {
  test('should create interview', async ({ 
    loggedInPage, 
    createdPositionId, 
    createdCandidateId 
  }) => {
    // Test implementation using pre-created test data
  });
});
```

### Custom Assertions

```typescript
import { createAssertions } from '../utils/assertions';

test('should show notification', async ({ page }) => {
  const assertions = createAssertions(page);
  
  // Trigger action
  await page.click('button');
  
  // Use custom assertion
  await assertions.expectSuccessNotification();
  await assertions.expectModalOpen('Confirmation');
});
```

## 🔄 CI/CD Integration

### GitHub Actions

The project includes two GitHub Actions workflows:

1. **`e2e-tests.yml`** - Full E2E test suite
   - Runs on push to main/develop branches
   - Tests all browsers (Chromium, Firefox, WebKit)
   - Includes visual regression tests
   - Publishes test results and reports

2. **`test-on-pr.yml`** - PR-specific testing
   - Runs smoke tests on all PRs
   - Runs full tests only if test files changed
   - Comments results on PR

### Manual Workflow Dispatch

You can manually trigger tests from GitHub Actions:

1. Go to Actions tab in GitHub
2. Select "E2E Tests" workflow
3. Click "Run workflow"
4. Choose options:
   - Test pattern (optional)
   - Browser (all, chromium, firefox, webkit)

### Test Results

- **HTML Reports**: Available as artifacts in GitHub Actions
- **JUnit XML**: For integration with other tools
- **JSON Results**: For programmatic analysis
- **Screenshots**: Captured on test failures

## 🐳 Docker Support

### Running Tests in Docker

```bash
# Build and run tests
./scripts/run-tests-docker.sh

# Run with options
./scripts/run-tests-docker.sh --headed --workers=1
./scripts/run-tests-docker.sh --project=chromium --grep="auth"
```

### Docker Compose

The `docker-compose.test.yml` file sets up:
- Frontend application
- Backend API
- PostgreSQL database
- Playwright test runner

### Docker Commands

```bash
# Start services
docker-compose -f docker-compose.test.yml up -d

# Run tests
docker-compose -f docker-compose.test.yml exec playwright-tests npm run test:e2e

# View logs
docker-compose -f docker-compose.test.yml logs

# Clean up
docker-compose -f docker-compose.test.yml down -v
```

## 🔧 Configuration

### Playwright Configuration

Key settings in `playwright.config.ts`:

- **Browsers**: Chromium, Firefox, WebKit, Mobile
- **Parallel Execution**: 50% of CPU cores (2 workers in CI)
- **Timeouts**: 30s action, 60s test
- **Base URL**: http://localhost:5173
- **Screenshots**: On failure
- **Videos**: On first retry

### Environment Variables

- `CI`: Set to `true` in CI environments
- `BASE_URL`: Frontend application URL
- `API_BASE_URL`: Backend API URL
- `DATABASE_URL`: PostgreSQL connection string

## 🐛 Troubleshooting

### Common Issues

1. **Tests timing out**
   ```bash
   # Increase timeout
   npx playwright test --timeout=90000
   ```

2. **Browser not found**
   ```bash
   # Reinstall browsers
   npx playwright install --with-deps
   ```

3. **Database connection issues**
   ```bash
   # Check database is running
   pg_isready -h localhost -p 5432
   
   # Reinitialize test data
   psql -d smarthr_test -f tests/fixtures/test-db-init.sql
   ```

4. **Port conflicts**
   ```bash
   # Check what's using ports
   lsof -i :5173
   lsof -i :8080
   ```

### Debug Mode

```bash
# Run in debug mode
npm run test:e2e:debug

# Debug specific test
npx playwright test auth.spec.ts --debug
```

### Verbose Logging

```bash
# Enable verbose logging
DEBUG=pw:api npx playwright test
```

### Performance Issues

```bash
# Run with performance monitoring
npm run test:e2e -- --reporter=./tests/utils/test-reporter.ts
```

## 📊 Test Analysis

### Analyzing Results

```bash
# Analyze test results
npm run test:analyze
```

### Performance Monitoring

The test suite includes performance monitoring:
- Page load times
- API response times
- Memory usage
- Network requests

### Flaky Test Detection

The custom reporter identifies:
- Tests that fail intermittently
- Slow-running tests
- Common error patterns

## 🤝 Contributing

When adding new tests:

1. Follow the existing test structure
2. Use Page Object Models for UI interactions
3. Add appropriate assertions
4. Include test data cleanup
5. Update documentation

### Test Naming Convention

- Describe what the test does: `should display job list`
- Use present tense: `should create candidate`
- Be specific: `should filter candidates by location`

### Best Practices

- Keep tests independent and isolated
- Use meaningful test data
- Add appropriate waits and assertions
- Handle different screen sizes
- Test error scenarios
- Clean up test data

## 📚 Resources

- [Playwright Documentation](https://playwright.dev/)
- [Playwright Best Practices](https://playwright.dev/docs/best-practices)
- [Page Object Model Pattern](https://playwright.dev/docs/pom)
- [Visual Testing Guide](https://playwright.dev/docs/test-snapshots)
