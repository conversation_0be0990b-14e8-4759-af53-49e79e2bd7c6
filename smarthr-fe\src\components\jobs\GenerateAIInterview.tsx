import React, { useState, useEffect } from 'react';
import { <PERSON>, Typo<PERSON>, Button, Checkbox, Row, Col, Input, Spin, message, Divider, Tag, Radio, Modal } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import { api } from '../../utils/api';
import { useJobContext } from '../../contexts/JobContext';
import { useMsal } from '@azure/msal-react';

const { Title, Text } = Typography;

interface Question {
  question: string;
  mid_answer: string;
  junior_answer: string;
  senior_answer: string;
  question_number: number;
  tag: string | undefined;
}

interface InterviewQuestions {
  id: string;
  position_id: string;
  data: {
    questions: Question[];
  };
  allow_regeneration: boolean;
  created_at: string;
  created_by: string;
  updated_at: string;
  updated_by: string;
}

interface GenerateAIInterviewProps {
  id: string;
}

const GenerateAIInterview: React.FC<GenerateAIInterviewProps> = ({ id }) => {
  const { jobData } = useJobContext();
  const { instance } = useMsal();
  const activeAccount = instance.getActiveAccount();
  const [numberOfQuestions, setNumberOfQuestions] = useState<number>(20);
  const [skills, setSkills] = useState({
    technical: true,
    softSkills: true,
    methodologies: true,
    languageTools: true
  });
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const [questions, setQuestions] = useState<InterviewQuestions | null>(null);
  const [allQuestions, setAllQuestions] = useState<InterviewQuestions | null>(null);
  const [loadingQuestions, setLoadingQuestions] = useState<boolean>(false);
  const [currentTag, setCurrentTag] = useState<string>('all');
  const [showSaveModal, setShowSaveModal] = useState<boolean>(false);

  // Calculate the max height for the questions card based on allow_regeneration
  const questionsCardMaxHeight =
    questions && typeof questions.allow_regeneration === 'boolean'
      ? questions.allow_regeneration
        ? 'calc(100vh - 470px)'
        : 'calc(100vh - 200px)'
      : 'calc(100vh - 200px)';

  // Add state for selected seniority level
  const [seniorityLevel, setSeniorityLevel] = useState<'junior' | 'mid' | 'senior'>('mid');

  useEffect(() => {
    // Attempt to fetch existing questions on component mount
    fetchQuestions();

    // Set default seniority level based on job data if available
    if (jobData?.position_info?.seniority?.name) {
      const jobSeniority = jobData.position_info.seniority.name.toLowerCase();
      if (jobSeniority.includes('junior')) {
        setSeniorityLevel('junior');
      } else if (jobSeniority.includes('senior')) {
        setSeniorityLevel('senior');
      } else {
        setSeniorityLevel('mid');
      }
    }
  }, [id, jobData]);

  const fetchQuestions = async () => {
    try {
      setLoadingQuestions(true);
      const response = await api.get(`/interview/questions/?position_id=${id}`);
      setQuestions(response.data);
      setAllQuestions(response.data);
    } catch (error) {
      console.error('Failed to fetch questions:', error);
      setQuestions(null);
      setAllQuestions(null);
    } finally {
      setLoadingQuestions(false);
    }
  };

  const handleSkillChange = (skill: keyof typeof skills) => {
    setSkills({
      ...skills,
      [skill]: !skills[skill]
    });
  };

  const handleGenerateInterview = async () => {
    try {
      // Validate number of questions
      if (numberOfQuestions < 1 || numberOfQuestions > 20) {
        message.error('You can select a maximum of 20 questions per position.');
        return;
      }
      setIsGenerating(true);

      // Prepare the payload based on selected skills
      const categories: string[] = [];
      if (skills.technical) categories.push('Technical Skills');
      if (skills.softSkills) categories.push('Soft Skills');
      if (skills.methodologies) categories.push('Methodologies');
      if (skills.languageTools) categories.push('Language - Tools');

      // Make the API call to generate questions
      await api.post(
        `/interview/${id}/questions?n_questions=${numberOfQuestions}&include=${categories.join(',')}&current_user=${
          activeAccount?.username
        }`
      );
      // Show success message
      message.success('Interview questions generated successfully!');

      // Fetch the newly generated questions
      await fetchQuestions();
    } catch (error) {
      console.error('Failed to generate interview:', error);
      message.error('Failed to generate interview questions.');
    } finally {
      setIsGenerating(false);
    }
  };

  // Get the answer based on seniority level
  const getAnswerBySeniority = (question: Question) => {
    switch (seniorityLevel) {
      case 'junior':
        return question.junior_answer;
      case 'mid':
        return question.mid_answer;
      case 'senior':
        return question.senior_answer;
      default:
        return question.mid_answer;
    }
  };

  async function handleSaveQuestions(confirm: boolean): Promise<void> {
    if (confirm) {
      try {
        setIsGenerating(true);
        // Make the API call to save questions
        await api.put(
          `/interview/questions/status?position_id=${questions?.position_id}&question_id=${questions?.id}&allow_regeneration=false`
        );
        // Show success message
        message.success('Interview questions saved successfully!');
        // Fetch the newly generated questions
        await fetchQuestions();
        setShowSaveModal(false);
      } catch (error) {
        console.error('Failed to save questions:', error);
        message.error('Failed to save interview questions.');
      } finally {
        setIsGenerating(false);
      }
    } else {
      setShowSaveModal(true);
    }
  }

  return (
    <>
      {!loadingQuestions && (!questions || questions.allow_regeneration) ? (
        <Card style={{ padding: 12, borderRadius: 8, marginBottom: 16, margin: '0 auto' }}>
          <Title level={4} style={{ marginTop: 0, marginBottom: 24 }}>
            Generate Interview
          </Title>

          <Row>
            <Col>
              <Row align='middle' style={{ marginBottom: 16 }}>
                <Col flex='auto'>
                  <Text strong>Number of questions</Text>
                </Col>
                <Col>
                  <Input
                    type='number'
                    value={numberOfQuestions}
                    onChange={(e) => setNumberOfQuestions(Number(e.target.value))}
                    style={{ width: 80 }}
                    min={1}
                    max={20}
                    size='small'
                  />
                </Col>
              </Row>

              <Row gutter={[8, 8]} style={{ marginBottom: 20 }}>
                <Col span={12}>
                  <Checkbox checked={skills.technical} onChange={() => handleSkillChange('technical')}>
                    Technical
                  </Checkbox>
                </Col>
                <Col span={12}>
                  <Checkbox checked={skills.softSkills} onChange={() => handleSkillChange('softSkills')}>
                    Soft Skills
                  </Checkbox>
                </Col>
                <Col span={12}>
                  <Checkbox checked={skills.methodologies} onChange={() => handleSkillChange('methodologies')}>
                    Methodologies
                  </Checkbox>
                </Col>
                <Col span={12}>
                  <Checkbox checked={skills.languageTools} onChange={() => handleSkillChange('languageTools')}>
                    Language/Tools
                  </Checkbox>
                </Col>
              </Row>

              <Button
                type='primary'
                onClick={handleGenerateInterview}
                loading={isGenerating}
                style={{ backgroundColor: '#673ab7', width: '50%' }}
                size='middle'
              >
                Generate AI Interview
              </Button>
            </Col>
            <Col span={10}></Col>
          </Row>
        </Card>
      ) : null}

      {loadingQuestions ? (
        <Card style={{ padding: 24, borderRadius: 8, textAlign: 'center' }}>
          <Spin size='large' />
        </Card>
      ) : allQuestions && allQuestions.data && allQuestions.data.questions.length > 0 ? (
        <Card style={{ padding: '0 24px', borderRadius: 8, overflowY: 'auto', maxHeight: questionsCardMaxHeight }}>
          <Row justify='space-between' align='middle' style={{ marginBottom: 8 }}>
            <Col>
              <Title level={4} style={{ margin: 0 }}>
                Interview Questions
              </Title>
            </Col>
          </Row>

          <Row gutter={16} align='middle' style={{ marginBottom: 16, flexWrap: 'wrap' }}>
            <Col xs={24} sm={12} style={{ marginBottom: 8 }}>
              <Text strong style={{ marginRight: 8 }}>
                Seniority:
              </Text>
              <Radio.Group
                value={seniorityLevel}
                onChange={(e) => setSeniorityLevel(e.target.value)}
                optionType='button'
                buttonStyle='solid'
                size='small'
              >
                <Radio.Button value='junior'>Junior</Radio.Button>
                <Radio.Button value='mid'>Mid</Radio.Button>
                <Radio.Button value='senior'>Senior</Radio.Button>
              </Radio.Group>
            </Col>
            <Col xs={24} sm={12} style={{ marginBottom: 8 }}>
              <Text strong style={{ marginRight: 8 }}>
                Tag:
              </Text>
              <Radio.Group
                value={currentTag}
                onChange={(e) => {
                  setCurrentTag(e.target.value);
                  if (e.target.value === 'all') {
                    setQuestions(allQuestions);
                    return;
                  }
                  const filteredQuestions =
                    allQuestions?.data.questions.filter((q) =>
                      q.tag?.toLowerCase().includes(e.target.value.toLowerCase())
                    ) || [];
                  if (allQuestions) {
                    setQuestions({
                      ...allQuestions,
                      data: { questions: filteredQuestions || [] }
                    });
                  }
                }}
                optionType='button'
                buttonStyle='solid'
                size='small'
              >
                <Radio.Button value='all'>All</Radio.Button>
                <Radio.Button value='tech'>Technical</Radio.Button>
                <Radio.Button value='soft'>Soft</Radio.Button>
                <Radio.Button value='metho'>Methodology</Radio.Button>
                <Radio.Button value='lang'>Language</Radio.Button>
              </Radio.Group>
            </Col>
          </Row>

          {/* {questions.data.questions[0].question_number === 1 && (
            <div style={{ marginBottom: 16 }}>
              <Tag color='#673ab7'>Language - Tools</Tag>
            </div>
          )} */}

          {questions?.data.questions.map((question) => (
            <div key={question.question_number} style={{ marginBottom: 32 }}>
              <div style={{ marginBottom: 4 }}>
                <Tag key={question.question_number} style={{ minWidth: 120 }} color='rgb(19 105 227)'>
                  {question?.tag?.toLocaleUpperCase()}
                </Tag>
              </div>
              <Text strong>
                {question.question_number}. {question.question}
              </Text>

              <div style={{ marginTop: 12, marginBottom: 12 }}>
                <Text style={{ display: 'block', marginBottom: 4 }}>Expected Response:</Text>
                <div style={{ backgroundColor: '#f9f9f9', padding: 12, borderRadius: 4 }}>
                  <Text>{getAnswerBySeniority(question)}</Text>
                </div>
              </div>

              <Divider style={{ margin: '12px 0' }} />
            </div>
          ))}

          {questions?.allow_regeneration && (
            <Col style={{ textAlign: 'right', marginBottom: 8 }}>
              <Button
                type='primary'
                onClick={() => handleSaveQuestions(false)}
                loading={isGenerating}
                style={{ backgroundColor: '#673ab7', minWidth: 140 }}
                size='middle'
              >
                Save Interview Questions
              </Button>
            </Col>
          )}
        </Card>
      ) : null}

      {/* Custom Save Interview Modal */}
      <Modal
        open={showSaveModal}
        onCancel={() => setShowSaveModal(false)}
        footer={null}
        closable={false}
        centered
        width={480}
        styles={{
          body: { padding: '32px 24px 24px 24px' }
        }}
      >
        <div style={{ textAlign: 'center' }}>
          {/* Header with close button */}
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '32px'
            }}
          >
            <Title level={4} style={{ margin: 0, fontSize: '18px', fontWeight: 600 }}>
              Save Interview
            </Title>
            <Button
              type='text'
              icon={<CloseOutlined />}
              onClick={() => setShowSaveModal(false)}
              style={{
                border: 'none',
                background: 'transparent',
                color: '#666',
                fontSize: '16px'
              }}
            />
          </div>

          {/* Warning Icon */}
          <div style={{ marginBottom: '24px' }}>
            <svg
              width='48'
              height='48'
              viewBox='0 0 48 48'
              fill='none'
              xmlns='http://www.w3.org/2000/svg'
              style={{ display: 'block', margin: '0 auto' }}
            >
              <path d='M24 4L44 40H4L24 4Z' fill='#ff4d4f' stroke='#ff4d4f' strokeWidth='2' strokeLinejoin='round' />
              <path d='M24 16V26' stroke='white' strokeWidth='2' strokeLinecap='round' />
              <circle cx='24' cy='32' r='1.5' fill='white' />
            </svg>
          </div>

          {/* Warning Text */}
          <div style={{ marginBottom: '32px' }}>
            <Text
              style={{
                fontSize: '16px',
                color: '#666',
                lineHeight: '1.5'
              }}
            >
              Once you save you won't be able to eliminate or edit this interview
            </Text>
          </div>

          {/* Action Buttons */}
          <div
            style={{
              display: 'flex',
              gap: '12px',
              justifyContent: 'right'
            }}
          >
            <Button
              onClick={() => setShowSaveModal(false)}
              style={{
                borderRadius: '8px',
                padding: '8px 24px',
                height: 'auto',
                fontSize: '14px',
                fontWeight: 500,
                border: '1px solid #d9d9d9',
                color: '#666'
              }}
            >
              Cancel
            </Button>
            <Button
              type='primary'
              loading={isGenerating}
              onClick={() => handleSaveQuestions(true)}
              style={{
                borderRadius: '8px',
                padding: '8px 24px',
                height: 'auto',
                fontSize: '14px',
                fontWeight: 500,
                backgroundColor: '#673ab7',
                borderColor: '#673ab7'
              }}
            >
              Save
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default GenerateAIInterview;
