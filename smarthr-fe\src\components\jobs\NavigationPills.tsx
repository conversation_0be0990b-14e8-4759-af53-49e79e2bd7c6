import React from 'react';
import { UserOutlined, CheckCircleOutlined } from '@ant-design/icons';
import { NAVIGATION_PILL_STYLES } from '../../utils/candidateStyles';

interface NavigationPillsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

export const NavigationPills: React.FC<NavigationPillsProps> = ({ activeTab, onTabChange }) => {
  const pills = [
    {
      key: 'hr',
      label: 'HR interview',
      icon: UserOutlined
    },
    {
      key: 'tech',
      label: 'Technical interview',
      icon: CheckCircleOutlined
    }
  ];

  return (
    <div style={NAVIGATION_PILL_STYLES.container}>
      {pills.map(({ key, label, icon: Icon }) => (
        <div
          key={key}
          onClick={() => onTabChange(key)}
          style={NAVIGATION_PILL_STYLES.pill(activeTab === key)}
        >
          <Icon style={NAVIGATION_PILL_STYLES.icon(activeTab === key)} />
          {label}
        </div>
      ))}
    </div>
  );
};

export default NavigationPills;