import React, { useState, useEffect } from 'react';
import { Popover, Form, Select, DatePicker, Button, Space, Row, Col } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import type { CandidateFilter, JobFilter, FilterOption, FilterConfiguration } from '../../types/filters';
import dayjs from 'dayjs';

const { Option } = Select;

interface FilterPopoverProps {
  children: React.ReactElement;
  filterType: 'candidate' | 'job';
  filters: CandidateFilter | JobFilter;
  onFiltersChange: (filters: CandidateFilter | JobFilter) => void;
  onApplyFilters: () => void;
  onClearFilters: () => void;
  availableOptions?: {
    roles?: FilterOption[];
    countries?: FilterOption[];
    createdBy?: FilterOption[];
    clients?: FilterOption[];
    locations?: FilterOption[];
  };
  loading?: boolean;
  filterConfig?: FilterConfiguration;
}

export const FilterPopover: React.FC<FilterPopoverProps> = ({
  children,
  filterType,
  filters,
  onFiltersChange,
  onApplyFilters,
  onClearFilters,
  availableOptions = {},
  loading = false,
  filterConfig = {}
}) => {
  const [form] = Form.useForm();
  const [localFilters, setLocalFilters] = useState(filters);

  // Update local filters when external filters change
  useEffect(() => {
    setLocalFilters(filters);
    form.setFieldsValue(getInitialValues());
  }, [filters, form]);

  const handleValuesChange = (changedValues: any, allValues: any) => {
    const updatedFilters = { ...localFilters };
    
    // Handle date fields
    if (changedValues.createdFrom !== undefined) {
      updatedFilters.createdFrom = changedValues.createdFrom ? changedValues.createdFrom.format('YYYY-MM-DD') : '';
    }
    if (changedValues.createdTo !== undefined) {
      updatedFilters.createdTo = changedValues.createdTo ? changedValues.createdTo.format('YYYY-MM-DD') : '';
    }
    
    // Handle other filter changes based on filter type
    if (filterType === 'candidate') {
      const candidateFilters = updatedFilters as CandidateFilter;

      candidateFilters.status = allValues.status || 'all';
      candidateFilters.role = allValues.role || '';
      candidateFilters.country = allValues.country || '';
      candidateFilters.createdBy = allValues.createdBy || '';
    } else {
      const jobFilters = updatedFilters as JobFilter;
      
      jobFilters.clientName = allValues.clientName || '';
      jobFilters.location = allValues.location || '';
      jobFilters.stage = allValues.stage || 'all';
    }
    
    setLocalFilters(updatedFilters);
  };

  const handleApply = () => {
    onFiltersChange(localFilters);
    onApplyFilters();
  };

  const handleClearAll = () => {
    let clearedFilters: CandidateFilter | JobFilter;
    
    if (filterType === 'candidate') {
      clearedFilters = {
        status: 'all',
        role: '',
        country: '',
        createdBy: '',
        createdFrom: '',
        createdTo: '',
        searchTerm: ''
      } as CandidateFilter;
    } else {
      clearedFilters = {
        stage: 'all',
        clientName: '',
        location: '',
        createdFrom: '',
        createdTo: '',
        searchTerm: ''
      } as JobFilter;
    }
    
    setLocalFilters(clearedFilters);
    form.setFieldsValue(getInitialValues(clearedFilters));
    onClearFilters();
  };

  const getInitialValues = (customFilters?: CandidateFilter | JobFilter) => {
    const currentFilters = customFilters || localFilters;
    const values: any = {
      createdFrom: currentFilters.createdFrom ? dayjs(currentFilters.createdFrom) : undefined,
      createdTo: currentFilters.createdTo ? dayjs(currentFilters.createdTo) : undefined,
    };

    if (filterType === 'candidate') {
      const candidateFilters = currentFilters as CandidateFilter;
      values.status = candidateFilters.status || 'all';
      values.role = candidateFilters.role || undefined;
      values.country = candidateFilters.country || undefined;
      values.createdBy = candidateFilters.createdBy || undefined;
    } else {
      const jobFilters = currentFilters as JobFilter;
      values.clientName = jobFilters.clientName || undefined;
      values.location = jobFilters.location || undefined;
      values.stage = jobFilters.stage || 'all';
    }

    return values;
  };

  const getCandidateFilters = () => (
    <>
      {/* Status Filter - conditionally rendered */}
      {filterConfig.status?.visible !== false && (
        <>
          <div style={{ marginBottom: 6, fontSize: '14px', color: '#666', fontWeight: '500' }}>
            {filterConfig.status?.label || 'Status'}
          </div>
          <Form.Item name="status" style={{ marginBottom: 12 }}>
            <Select
              placeholder={filterConfig.status?.placeholder || "All"}
              style={{
                width: '100%'
              }}
              size="small"
            >
              <Option value="all">All</Option>
              <Option value="enabled">Enabled</Option>
              <Option value="disabled">Disabled</Option>
            </Select>
          </Form.Item>
        </>
      )}

      {/* Role Filter - conditionally rendered */}
      {filterConfig.role?.visible !== false && (
        <>
          <div style={{ marginBottom: 6, fontSize: '14px', color: '#666', fontWeight: '500' }}>
            {filterConfig.role?.label || 'Role'}
          </div>
          <Form.Item name="role" style={{ marginBottom: 12 }}>
            <Select
              placeholder={filterConfig.role?.placeholder || "Select role"}
              allowClear
              showSearch
              style={{
                width: '100%'
              }}
              size="small"
              filterOption={(input, option) =>
                (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
              }
            >
              {availableOptions.roles?.map(role => (
                <Option key={role.value} value={role.value}>
                  {role.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </>
      )}

      {/* Location Filter - conditionally rendered */}
      {filterConfig.country?.visible !== false && (
        <>
          <div style={{ marginBottom: 6, fontSize: '14px', color: '#666', fontWeight: '500' }}>
            {filterConfig.country?.label || 'Location'}
          </div>
          <Form.Item name="country" style={{ marginBottom: 12 }}>
            <Select
              placeholder={filterConfig.country?.placeholder || "Select location"}
              allowClear
              showSearch
              style={{
                width: '100%'
              }}
              size="small"
              filterOption={(input, option) =>
                (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
              }
            >
              {availableOptions.countries?.map(country => (
                <Option key={country.value} value={country.value}>
                  {country.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </>
      )}

      {/* Created By Filter - conditionally rendered */}
      {filterConfig.createdBy?.visible !== false && (
        <>
          <div style={{ marginBottom: 6, fontSize: '14px', color: '#666', fontWeight: '500' }}>
            {filterConfig.createdBy?.label || 'Created By'}
          </div>
          <Form.Item name="createdBy" style={{ marginBottom: 12 }}>
            <Select
              placeholder={filterConfig.createdBy?.placeholder || "Select created by"}
              allowClear
              showSearch
              style={{
                width: '100%'
              }}
              size="small"
              filterOption={(input, option) =>
                (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
              }
            >
              {availableOptions.createdBy?.map(createdBy => (
                <Option key={createdBy.value} value={createdBy.value}>
                  {createdBy.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </>
      )}
    </>
  );

  const getJobFilters = () => (
    <>
      <div style={{ marginBottom: 6, fontSize: '14px', color: '#666', fontWeight: '500' }}>
        Client
      </div>
      <Form.Item name="clientName" style={{ marginBottom: 12 }}>
        <Select 
          placeholder="Select client" 
          allowClear
          showSearch
          style={{ 
            width: '100%'
          }}
          size="small"
          filterOption={(input, option) =>
            (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
          }
        >
          {availableOptions.clients?.map(client => (
            <Option key={client.value} value={client.value}>
              {client.label}
            </Option>
          ))}
        </Select>
      </Form.Item>

      <div style={{ marginBottom: 6, fontSize: '14px', color: '#666', fontWeight: '500' }}>
        Location
      </div>
      <Form.Item name="location" style={{ marginBottom: 12 }}>
        <Select 
          placeholder="Select location" 
          allowClear
          showSearch
          style={{ 
            width: '100%'
          }}
          size="small"
          filterOption={(input, option) =>
            (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
          }
        >
          {availableOptions.locations?.map(location => (
            <Option key={location.value} value={location.value}>
              {location.label}
            </Option>
          ))}
        </Select>
      </Form.Item>

      <div style={{ marginBottom: 6, fontSize: '14px', color: '#666', fontWeight: '500' }}>
        Stage
      </div>
      <Form.Item name="stage" style={{ marginBottom: 12 }}>
        <Select 
          placeholder="All" 
          style={{ 
            width: '100%'
          }}
          size="small"
        >
          <Option value="all">All</Option>
          <Option value="open">Open</Option>
          <Option value="closed">Closed</Option>
        </Select>
      </Form.Item>
    </>
  );

  const content = (
    <div style={{ 
      width: 274, 
      padding: '15px',
      background: '#fff',
      borderRadius: '10px',
      border: '1px solid #7B66FF',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
    }}>
      <Form
        form={form}
        layout="vertical"
        onValuesChange={handleValuesChange}
        initialValues={getInitialValues()}
      >
        {/* Clear all filters link */}
        <div style={{ marginBottom: 12, textAlign: 'right' }}>
          <Button 
            type="link" 
            onClick={handleClearAll}
            style={{ 
              padding: 0, 
              height: 'auto',
              fontSize: '12px',
              color: '#7B66FF'
            }}
          >
            Clear all filters
          </Button>
        </div>

        {/* Filter Fields with Labels */}
        {filterType === 'candidate' ? getCandidateFilters() : getJobFilters()}

        {/* Date Range - conditionally rendered */}
        {(filterConfig.createdFrom?.visible !== false || filterConfig.createdTo?.visible !== false) && (
          <Row gutter={8} style={{ marginBottom: 12 }}>
            {filterConfig.createdFrom?.visible !== false && (
              <Col span={filterConfig.createdTo?.visible !== false ? 12 : 24}>
                <div style={{ marginBottom: 6, fontSize: '14px', color: '#666', fontWeight: '500' }}>
                  {filterConfig.createdFrom?.label || 'Date from'}
                </div>
                <Form.Item name="createdFrom" style={{ marginBottom: 0 }}>
                  <DatePicker
                    style={{
                      width: '100%',
                      borderRadius: '10px'
                    }}
                    format="DD/MM/YYYY"
                    size="small"
                    placeholder={filterConfig.createdFrom?.placeholder || "Select date"}
                  />
                </Form.Item>
              </Col>
            )}
            {filterConfig.createdTo?.visible !== false && (
              <Col span={filterConfig.createdFrom?.visible !== false ? 12 : 24}>
                <div style={{ marginBottom: 6, fontSize: '14px', color: '#666', fontWeight: '500' }}>
                  {filterConfig.createdTo?.label || 'to'}
                </div>
                <Form.Item name="createdTo" style={{ marginBottom: 0 }}>
                  <DatePicker
                    style={{
                      width: '100%',
                      borderRadius: '10px'
                    }}
                    format="DD/MM/YYYY"
                    size="small"
                    placeholder={filterConfig.createdTo?.placeholder || "Select date"}
                  />
                </Form.Item>
              </Col>
            )}
          </Row>
        )}

        {/* Search Button */}
        <Button
          type="primary"
          block
          icon={<SearchOutlined />}
          onClick={handleApply}
          loading={loading}
          style={{
            background: '#7B66FF',
            border: '1px solid #7B66FF',
            borderRadius: '10px',
            height: '32px',
            fontSize: '14px',
            fontWeight: '500',
            marginTop: '8px'
          }}
        >
          Search
        </Button>
      </Form>
    </div>
  );

  return (
    <Popover
      content={content}
      trigger="click"
      placement="bottomRight"
      overlayStyle={{ zIndex: 1000 }}
      overlayInnerStyle={{ 
        padding: 0,
        border: 'none',
        borderRadius: '10px',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
      }}
    >
      {children}
    </Popover>
  );
}; 