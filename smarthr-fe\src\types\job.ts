export interface PositionInfo {
  positionName: string;
  roleName: string;
  jobDescription: string;
  mainResponsabilities: string;
  openPositionSkills: { skillName: string }[];
  positionAllocations: { Name: string }[];
  clientName: string;
  seniority?: { name: string };
  positionTypeName: string;
  modifiedBy?: string;
  createdBy?: string;
  positionStartDate?: string;
  positionCloseDate?: string;
  reasonStatus?: {
    id: string;
    reason: string;
  };
}

export interface Job {
  id: string;
  proj_id: string;
  created_at: string;
  updated_at: string;
  last_matching: string | null;
  question_answers: any;
  top_candidates: any[];

  position_info: {
    roleName: string;
    createdBy: string;
    clientName: string;
    projectName: string;
    positionName: string;
    positionTypeName: string;
    jobDescription: string;
    positionCreateDate: string;
    positionStartDate: string;
    positionCloseDate: string;
    seniority?: {
      name: string;
    };
    reasonStatus?: {
      reason: string | null;
    };
    openPositionSkills: {
      skillName: string;
      skillScore: number | null;
      skillCategory: string;
      skillLeveName: string;
    }[];
    positionAllocations: {
      Name: string;
      isoCode: string | null;
    }[];
    mainResponsabilities: string;
  };
}

export interface PositionsResponse {
  total_items: number;
  items: Job[];
}

export interface MatchedCandidate {
  id: string;
  candidate_info: {
    personal_info: {
      full_name: string;
      email: string;
    };
    skills: { name: string }[];
    education: { degree: string }[];
    languages: { language: string }[];
    certifications: { name: string }[];
    work_experience: { job_title: string }[];
    hobbies_interests: string[];
  };
  analysis: {
    Score: number;
    LLM_Analysis: {
      reason: string;
      skill_match_analysis?: {
        [skill: string]: {
          coef_match: number;
          comment: string;
        };
      };
      skill_not_matched?: string[];
    };
  };
  cosine_similarity: number;
  custom_analysis?: {
    compatibilityPercentage: number;
    justification: string;
    matchesFound: string[];
    missing: string[];
    recommendation: string;
  };
}
