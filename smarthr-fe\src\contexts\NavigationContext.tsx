import React, { createContext, useContext, useState, ReactNode } from 'react';
import { useNavigate } from 'react-router-dom';

interface CandidatesTableState {
  searchTerm: string | null;
  page: number;
  limit: number;
  status?: string;
  country: string;
  role: string;
  createdFrom: string;
  createdTo: string;
  // Add any other filters or state that need to be preserved
}

interface JobOrdersState {
  searchTerm: string;
  page: number;
  limit: number;
  stage?: string;
  clientName: string;
  location: string;
  createdFrom: string;
  createdTo: string;
}

interface NavigationState {
  previousPath: string | null;
  candidatesTableState: CandidatesTableState | null;
  jobOrdersState: JobOrdersState | null;
}

interface NavigationContextType {
  navigationState: NavigationState;
  setPreviousPath: (path: string) => void;
  setCandidatesTableState: (state: CandidatesTableState) => void;
  setJobOrdersState: (state: JobOrdersState) => void;
  navigateBack: () => void;
  clearNavigationState: () => void;
}

const NavigationContext = createContext<NavigationContextType | undefined>(undefined);

export const NavigationProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const navigate = useNavigate();
  const [navigationState, setNavigationState] = useState<NavigationState>({
    previousPath: null,
    candidatesTableState: null,
    jobOrdersState: null,
  });

  const setPreviousPath = (path: string) => {
    setNavigationState(prev => ({
      ...prev,
      previousPath: path
    }));
  };

  const setCandidatesTableState = (state: CandidatesTableState) => {
    setNavigationState(prev => ({
      ...prev,
      candidatesTableState: state
    }));
  };

  const setJobOrdersState = (state: JobOrdersState) => {
    setNavigationState(prev => ({
      ...prev,
      jobOrdersState: state
    }));
  };

  const navigateBack = () => {
    if (navigationState.previousPath) {
      const preservedState = navigationState.previousPath === '/candidates' 
        ? navigationState.candidatesTableState 
        : navigationState.jobOrdersState;
      
      navigate(navigationState.previousPath, { 
        state: { 
          preservedState 
        } 
      });
    } else {
      // Fallback to home if no previous path
      navigate('/');
    }
  };

  const clearNavigationState = () => {
    setNavigationState({
      previousPath: null,
      candidatesTableState: null,
      jobOrdersState: null,
    });
  };

  return (
    <NavigationContext.Provider value={{
      navigationState,
      setPreviousPath,
      setCandidatesTableState,
      setJobOrdersState,
      navigateBack,
      clearNavigationState
    }}>
      {children}
    </NavigationContext.Provider>
  );
};

export const useNavigation = () => {
  const context = useContext(NavigationContext);
  if (!context) {
    throw new Error('useNavigation must be used within a NavigationProvider');
  }
  return context;
};

export type { CandidatesTableState, JobOrdersState, NavigationState }; 