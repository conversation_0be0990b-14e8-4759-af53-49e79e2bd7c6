import { api, endpoints } from '../utils/api';
import { message } from 'antd';
import type { CandidateInfo } from '../types/candidate';

export const createCandidate = async (projectId: string, data: CandidateInfo) => {
  try {
    const response = await api.post(endpoints.candidates.create, {
      id: 'string',
      proj_id: projectId,
      candidate_info: data,
      suggested_positions: [{}],
      analysis_status: 'string'
    });
    message.success('Candidate created successfully!');
    return response;
  } catch (error) {
    console.error('Error creating candidate:', error);
    message.error('Error creating candidate.');
    throw error;
  }
};

export const updateCandidate = async (payload: any) => {
  try {
    const response = await api.put(endpoints.candidates.update, payload);
    message.success('Candidate updated successfully!');
    return response;
  } catch (error) {
    console.error('Error updating candidate:', error);
    message.error('Error updating candidate.');
    throw error;
  }
};

export const validateCandidate = async (projectId: string, data: CandidateInfo) => {
  try {
    const response = await api.post(endpoints.candidates.validate, {
      project_id: projectId,
      candidate_info: data,
      keys_to_eval: ['personal_info.full_name', 'personal_info.email']
    });
    return response.data;
  } catch (error) {
    console.error('Validation error:', error);
    message.error('Candidate data validation failed.');
    throw error;
  }
};
