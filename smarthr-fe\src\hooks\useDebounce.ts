import { useState, useEffect } from 'react';

export function useDebounce<T>(initialValue: T, delay: number): [T, (value: T) => void] {
  const [actualValue, setActualValue] = useState<T>(initialValue);
  const [debouncedValue, setDebouncedValue] = useState<T>(initialValue);

  useEffect(() => {
    const debounceId = setTimeout(() => setDebouncedValue(actualValue), delay);
    return () => clearTimeout(debounceId);
  }, [actualValue, delay]);

  return [debouncedValue, setActualValue];
} 