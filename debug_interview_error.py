#!/usr/bin/env python3
"""
Debug script to identify the 500 error in interview evaluation.
"""

import sys
import os
import traceback

# Add the smarthr-be directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'smarthr-be'))

def test_interview_functions():
    """Test interview functions to identify the error"""
    try:
        print("🔍 Testing interview controller imports...")
        
        # Test basic imports
        from controllers.interview_controller import (
            update_interview_tec, 
            re_evaluate_interview,
            evaluate_interview_transcript_based,
            _validate_evaluation_result
        )
        print("✅ All imports successful")
        
        # Test models
        from models.interview import InterviewTec, Interview, EvaluationResult, QuestionEvaluation, Seniority
        print("✅ Model imports successful")
        
        # Test validation function with simple data
        print("\n🔍 Testing validation function...")
        result = EvaluationResult(
            overall_seniority=Seniority.JUNIOR,
            per_question=[],
            percentage_of_match=0.0,
            explanation="Test evaluation"
        )
        
        issues = _validate_evaluation_result(result, 0, "test")
        print(f"✅ Validation function works: {len(issues)} issues found")
        
        # Test if the issue is with the enhanced prompts
        print("\n🔍 Testing prompt formatting...")
        
        # Simulate the prompt creation that happens in evaluate_interview_transcript_based
        class MockTranscriptQuestions:
            def __init__(self):
                self.questions = [{"question_text": "Test question"}]
        
        transcript_questions = MockTranscriptQuestions()
        
        # Test the f-string formatting that's used in the prompts
        test_prompt = f"""
        Test prompt with {len(transcript_questions.questions)} questions.
        Another reference: {len(transcript_questions.questions)} questions total.
        """
        
        print(f"✅ Prompt formatting works: {len(test_prompt)} characters")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in interview functions: {e}")
        print("\n📋 Full traceback:")
        traceback.print_exc()
        return False

def test_database_connection():
    """Test database connection"""
    try:
        print("\n🔍 Testing database connection...")
        from controllers.interview_controller import get_cursor
        
        with get_cursor() as cur:
            cur.execute("SELECT 1")
            result = cur.fetchone()
            print(f"✅ Database connection works: {result}")
            return True
            
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        return False

def test_azure_openai():
    """Test Azure OpenAI connection"""
    try:
        print("\n🔍 Testing Azure OpenAI connection...")
        from utils.azure_openai_client import get_azure_openai_client
        
        client = get_azure_openai_client()
        print("✅ Azure OpenAI client created successfully")
        return True
        
    except Exception as e:
        print(f"❌ Azure OpenAI error: {e}")
        return False

def main():
    """Main debug function"""
    print("🚨 DEBUGGING INTERVIEW 500 ERROR")
    print("=" * 50)
    
    success = True
    
    # Test 1: Interview functions
    if not test_interview_functions():
        success = False
    
    # Test 2: Database connection
    if not test_database_connection():
        success = False
    
    # Test 3: Azure OpenAI
    if not test_azure_openai():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("✅ ALL TESTS PASSED - Issue might be runtime-specific")
        print("💡 Suggestion: Check server logs for the actual error")
    else:
        print("❌ FOUND ISSUES - See errors above")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
